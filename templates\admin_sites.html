<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Site Configuration Manager - <PERSON> Scraper</title>
    <link rel="icon" href="/static/media/favicon.ico" type="image/x-icon">
    <script src="https://unpkg.com/htmx.org@2.0.4"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .tab-content { display: none; }
        .tab-content.active { display: block; }
        .tab-button.active {
            background-color: #3b82f6;
            color: white;
        }
        .json-editor {
            font-family: 'Courier New', monospace;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 12px;
            min-height: 400px;
            white-space: pre-wrap;
            overflow-x: auto;
        }
        .element-card {
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 16px;
            background-color: #f9fafb;
        }
        .website-card {
            border: 2px solid #d1d5db;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            background-color: white;
        }
        .company-section {
            border: 3px solid #9ca3af;
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 24px;
            background-color: #f3f4f6;
        }
        .validation-error {
            background-color: #fef2f2;
            border: 1px solid #fecaca;
            color: #dc2626;
            padding: 12px;
            border-radius: 6px;
            margin-bottom: 16px;
        }
        .validation-success {
            background-color: #f0fdf4;
            border: 1px solid #bbf7d0;
            color: #16a34a;
            padding: 12px;
            border-radius: 6px;
            margin-bottom: 16px;
        }
        .loading {
            opacity: 0.6;
            pointer-events: none;
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen">
    <!-- Include authentication header -->
    {% include "auth_header.html" %}

    <div class="container mx-auto px-4 py-8">
        <header class="mb-8">
            <h1 class="text-4xl font-bold text-center text-blue-600">Site Configuration Manager</h1>
            <p class="text-center text-gray-600 mt-2">Manage scraping site configurations</p>
        </header>

        <nav class="flex justify-center space-x-6 mb-8">
            <a href="/admin" class="text-blue-600 font-medium hover:text-blue-800">Admin Panel</a>
            <a href="/manual-request" class="text-blue-600 font-medium hover:text-blue-800">Manual Request</a>
            <a href="/results" class="text-blue-600 font-medium hover:text-blue-800">View Results</a>
            <a href="/database" class="text-blue-600 font-medium hover:text-blue-800">View Database</a>
        </nav>

        <!-- Country Tabs -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="border-b border-gray-200 mb-6">
                <nav class="-mb-px flex space-x-8">
                    {% for country in countries %}
                    <button 
                        class="tab-button py-2 px-4 border-b-2 border-transparent text-sm font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300"
                        onclick="switchTab('{{ country.file }}')"
                        id="tab-{{ country.file }}"
                    >
                        {{ country.name }}
                    </button>
                    {% endfor %}
                </nav>
            </div>

            <!-- Tab Contents -->
            {% for country in countries %}
            <div id="content-{{ country.file }}" class="tab-content">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-2xl font-bold text-gray-800">{{ country.name }} Configuration</h2>
                    <div class="space-x-2">
                        <button 
                            onclick="loadConfig('{{ country.file }}')"
                            class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-md transition duration-300"
                        >
                            Reload
                        </button>
                        <button 
                            onclick="validateConfig('{{ country.file }}')"
                            class="bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-2 rounded-md transition duration-300"
                        >
                            Validate
                        </button>
                        <button 
                            onclick="saveConfig('{{ country.file }}')"
                            class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md transition duration-300"
                        >
                            Save Changes
                        </button>
                    </div>
                </div>

                <!-- Status Messages -->
                <div id="status-{{ country.file }}"></div>

                <!-- Configuration Editor -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- JSON Editor -->
                    <div>
                        <div class="flex justify-between items-center mb-3">
                            <h3 class="text-lg font-semibold">JSON Editor</h3>
                            <div class="space-x-2">
                                <button
                                    onclick="formatJson('{{ country.file }}')"
                                    class="text-sm bg-gray-200 hover:bg-gray-300 px-3 py-1 rounded"
                                >
                                    Format
                                </button>
                                <button
                                    onclick="syncFromVisual('{{ country.file }}')"
                                    class="text-sm bg-blue-200 hover:bg-blue-300 px-3 py-1 rounded"
                                >
                                    ← Sync from Visual
                                </button>
                            </div>
                        </div>
                        <textarea
                            id="editor-{{ country.file }}"
                            class="json-editor w-full"
                            rows="20"
                            placeholder="Loading configuration..."
                            oninput="syncToVisual('{{ country.file }}')"
                        ></textarea>
                    </div>

                    <!-- Visual Editor -->
                    <div>
                        <div class="flex justify-between items-center mb-3">
                            <h3 class="text-lg font-semibold">Visual Editor</h3>
                            <div class="space-x-2">
                                <button
                                    onclick="addNewElement('{{ country.file }}')"
                                    class="text-sm bg-green-500 hover:bg-green-600 text-white px-3 py-1 rounded"
                                >
                                    + Add Element
                                </button>
                                <button
                                    onclick="addNewWebsite('{{ country.file }}')"
                                    class="text-sm bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded"
                                >
                                    + Add Website
                                </button>
                            </div>
                        </div>
                        <div id="visual-editor-{{ country.file }}" class="border border-gray-300 rounded-lg p-4 bg-white min-h-96 overflow-y-auto">
                            <p class="text-gray-500 text-center">Load configuration to see visual editor</p>
                        </div>
                    </div>
                </div>

                <!-- Search and Filter -->
                <div class="mt-6 bg-gray-50 p-4 rounded-lg">
                    <h3 class="text-lg font-semibold mb-3">Search & Filter</h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <input
                            type="text"
                            id="search-{{ country.file }}"
                            placeholder="Search elements, products, UIDs..."
                            class="border border-gray-300 rounded px-3 py-2"
                            oninput="filterElements('{{ country.file }}')"
                        >
                        <select
                            id="filter-company-{{ country.file }}"
                            class="border border-gray-300 rounded px-3 py-2"
                            onchange="filterElements('{{ country.file }}')"
                        >
                            <option value="">All Companies</option>
                        </select>
                        <select
                            id="filter-product-{{ country.file }}"
                            class="border border-gray-300 rounded px-3 py-2"
                            onchange="filterElements('{{ country.file }}')"
                        >
                            <option value="">All Product Types</option>
                        </select>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>

    <script>
        let currentTab = null;
        let configData = {};

        // Initialize first tab
        document.addEventListener('DOMContentLoaded', function() {
            const firstCountry = {{ countries[0].file | tojson }};
            switchTab(firstCountry);
        });

        function switchTab(countryFile) {
            // Hide all tab contents
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // Remove active class from all tab buttons
            document.querySelectorAll('.tab-button').forEach(button => {
                button.classList.remove('active');
            });
            
            // Show selected tab content
            document.getElementById(`content-${countryFile}`).classList.add('active');
            document.getElementById(`tab-${countryFile}`).classList.add('active');
            
            currentTab = countryFile;
            
            // Load configuration if not already loaded
            if (!configData[countryFile]) {
                loadConfig(countryFile);
            }
        }

        function loadConfig(countryFile) {
            const editor = document.getElementById(`editor-${countryFile}`);
            const visualEditor = document.getElementById(`visual-editor-${countryFile}`);
            const statusDiv = document.getElementById(`status-${countryFile}`);
            
            // Show loading state
            editor.value = 'Loading...';
            editor.classList.add('loading');
            
            fetch(`/admin/sites/${countryFile}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        configData[countryFile] = data.data;
                        editor.value = JSON.stringify(data.data, null, 2);
                        generateVisualEditor(countryFile, data.data);
                        showStatus(countryFile, 'Configuration loaded successfully', 'success');
                    } else {
                        showStatus(countryFile, 'Failed to load configuration', 'error');
                    }
                })
                .catch(error => {
                    console.error('Error loading config:', error);
                    showStatus(countryFile, `Error loading configuration: ${error.message}`, 'error');
                })
                .finally(() => {
                    editor.classList.remove('loading');
                });
        }

        function saveConfig(countryFile) {
            const editor = document.getElementById(`editor-${countryFile}`);
            const statusDiv = document.getElementById(`status-${countryFile}`);
            
            try {
                // Validate JSON first
                const configJson = editor.value;
                JSON.parse(configJson);
                
                // Show loading state
                editor.classList.add('loading');
                showStatus(countryFile, 'Saving configuration...', 'info');
                
                const formData = new FormData();
                formData.append('config_data', configJson);
                
                fetch(`/admin/sites/${countryFile}`, {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        configData[countryFile] = JSON.parse(configJson);
                        generateVisualEditor(countryFile, configData[countryFile]);
                        showStatus(countryFile, data.message, 'success');
                    } else {
                        showStatus(countryFile, `Save failed: ${data.detail || 'Unknown error'}`, 'error');
                    }
                })
                .catch(error => {
                    console.error('Error saving config:', error);
                    showStatus(countryFile, `Error saving configuration: ${error.message}`, 'error');
                })
                .finally(() => {
                    editor.classList.remove('loading');
                });
                
            } catch (e) {
                showStatus(countryFile, `Invalid JSON: ${e.message}`, 'error');
            }
        }

        function validateConfig(countryFile) {
            const editor = document.getElementById(`editor-${countryFile}`);
            
            try {
                JSON.parse(editor.value);
                showStatus(countryFile, 'JSON syntax is valid', 'success');
            } catch (e) {
                showStatus(countryFile, `Invalid JSON: ${e.message}`, 'error');
            }
        }

        function showStatus(countryFile, message, type) {
            const statusDiv = document.getElementById(`status-${countryFile}`);
            const className = type === 'success' ? 'validation-success' : 
                             type === 'error' ? 'validation-error' : 
                             'bg-blue-50 border border-blue-200 text-blue-800 p-3 rounded-md mb-4';
            
            statusDiv.innerHTML = `<div class="${className}">${message}</div>`;
            
            // Auto-hide after 5 seconds for success messages
            if (type === 'success') {
                setTimeout(() => {
                    statusDiv.innerHTML = '';
                }, 5000);
            }
        }

        function generateVisualEditor(countryFile, data) {
            const visualEditor = document.getElementById(`visual-editor-${countryFile}`);
            let html = '';
            let companies = new Set();
            let productTypes = new Set();

            for (const [countryName, countryData] of Object.entries(data)) {
                html += `<div class="company-section" data-country="${countryName}">`;
                html += `<div class="flex justify-between items-center mb-4">`;
                html += `<h4 class="text-xl font-bold text-gray-800">${countryName}</h4>`;
                html += `<button onclick="deleteCountry('${countryFile}', '${countryName}')" class="text-red-500 hover:text-red-700 text-sm">Delete Country</button>`;
                html += `</div>`;

                for (const [companyName, companyData] of Object.entries(countryData)) {
                    companies.add(companyName);
                    html += `<div class="website-card" data-company="${companyName}">`;
                    html += `<div class="flex justify-between items-center mb-3">`;
                    html += `<h5 class="text-lg font-semibold text-gray-700">${companyName}</h5>`;
                    html += `<button onclick="deleteCompany('${countryFile}', '${countryName}', '${companyName}')" class="text-red-500 hover:text-red-700 text-sm">Delete Company</button>`;
                    html += `</div>`;

                    for (const [websiteName, websiteData] of Object.entries(companyData)) {
                        html += `<div class="element-card" data-website="${websiteName}">`;
                        html += `<div class="flex justify-between items-center mb-2">`;
                        html += `<h6 class="font-medium text-gray-600">${websiteName}</h6>`;
                        html += `<div class="space-x-2">`;
                        html += `<button onclick="editWebsite('${countryFile}', '${countryName}', '${companyName}', '${websiteName}')" class="text-blue-500 hover:text-blue-700 text-xs">Edit</button>`;
                        html += `<button onclick="deleteWebsite('${countryFile}', '${countryName}', '${companyName}', '${websiteName}')" class="text-red-500 hover:text-red-700 text-xs">Delete</button>`;
                        html += `</div>`;
                        html += `</div>`;

                        html += `<div class="mb-2">`;
                        html += `<label class="text-xs text-gray-500">URL:</label>`;
                        html += `<input type="text" value="${websiteData.url || ''}" onchange="updateWebsiteUrl('${countryFile}', '${countryName}', '${companyName}', '${websiteName}', this.value)" class="w-full text-sm border border-gray-200 rounded px-2 py-1 mt-1">`;
                        html += `</div>`;

                        if (websiteData.Elements) {
                            html += `<div class="mt-3">`;
                            html += `<div class="flex justify-between items-center mb-2">`;
                            html += `<p class="text-sm font-medium text-gray-600">Elements (${Object.keys(websiteData.Elements).length}):</p>`;
                            html += `<button onclick="addElementToWebsite('${countryFile}', '${countryName}', '${companyName}', '${websiteName}')" class="text-green-500 hover:text-green-700 text-xs">+ Add Element</button>`;
                            html += `</div>`;

                            for (const [elementName, elementData] of Object.entries(websiteData.Elements)) {
                                if (elementData.product) productTypes.add(elementData.product);

                                html += `<div class="bg-white p-3 rounded border mb-2 element-item" data-element="${elementName}" data-product="${elementData.product || ''}" data-uid="${elementData.uid || ''}" data-name="${elementData.name || ''}" data-price="${elementData.price || ''}">`;
                                html += `<div class="flex justify-between items-start mb-2">`;
                                html += `<span class="text-xs font-medium text-gray-500">${elementName}</span>`;
                                html += `<div class="space-x-1">`;
                                html += `<button onclick="editElement('${countryFile}', '${countryName}', '${companyName}', '${websiteName}', '${elementName}')" class="text-blue-500 hover:text-blue-700 text-xs">Edit</button>`;
                                html += `<button onclick="deleteElement('${countryFile}', '${countryName}', '${companyName}', '${websiteName}', '${elementName}')" class="text-red-500 hover:text-red-700 text-xs">Delete</button>`;
                                html += `</div>`;
                                html += `</div>`;

                                html += `<div class="grid grid-cols-2 gap-2 text-xs">`;
                                html += `<div><strong>Product:</strong> ${elementData.product || 'Unknown'}</div>`;
                                html += `<div><strong>Weight:</strong> ${elementData.weight_text || elementData.weight || 'No weight'}</div>`;
                                html += `<div><strong>UID:</strong> ${elementData.uid || 'No UID'}</div>`;
                                html += `<div><strong>Selector (name):</strong> ${elementData.name || elementData.selector || 'No selector'}</div>`;
                                html += `</div>`;

                                if (elementData.price || elementData.price_selector) {
                                    html += `<div class="mt-2 text-xs"><strong>Price Selector:</strong> ${elementData.price || elementData.price_selector}</div>`;
                                }

                                if (elementData.photo) {
                                    html += `<div class="mt-2 text-xs"><strong>Photo:</strong> ${elementData.photo}</div>`;
                                }

                                html += `</div>`;
                            }
                            html += `</div>`;
                        }

                        html += `</div>`;
                    }
                    html += `</div>`;
                }
                html += `</div>`;
            }

            visualEditor.innerHTML = html || '<p class="text-gray-500 text-center">No configuration data</p>';

            // Update filter dropdowns
            updateFilterDropdowns(countryFile, companies, productTypes);
        }

        function updateFilterDropdowns(countryFile, companies, productTypes) {
            const companySelect = document.getElementById(`filter-company-${countryFile}`);
            const productSelect = document.getElementById(`filter-product-${countryFile}`);

            // Update company dropdown
            companySelect.innerHTML = '<option value="">All Companies</option>';
            companies.forEach(company => {
                companySelect.innerHTML += `<option value="${company}">${company}</option>`;
            });

            // Update product type dropdown
            productSelect.innerHTML = '<option value="">All Product Types</option>';
            productTypes.forEach(product => {
                productSelect.innerHTML += `<option value="${product}">${product}</option>`;
            });
        }

        function filterElements(countryFile) {
            const searchTerm = document.getElementById(`search-${countryFile}`).value.toLowerCase();
            const companyFilter = document.getElementById(`filter-company-${countryFile}`).value;
            const productFilter = document.getElementById(`filter-product-${countryFile}`).value;

            const elements = document.querySelectorAll(`#visual-editor-${countryFile} .element-item`);
            const websites = document.querySelectorAll(`#visual-editor-${countryFile} .element-card`);
            const companies = document.querySelectorAll(`#visual-editor-${countryFile} .website-card`);

            elements.forEach(element => {
                const elementText = element.textContent.toLowerCase();
                const elementProduct = element.dataset.product;
                const elementUid = element.dataset.uid;
                const elementName = element.dataset.name;
                const elementPrice = element.dataset.price;
                const parentWebsite = element.closest('.element-card');
                const parentCompany = element.closest('.website-card');
                const companyName = parentCompany ? parentCompany.dataset.company : '';

                const matchesSearch = !searchTerm ||
                                    elementText.includes(searchTerm) ||
                                    elementUid.toLowerCase().includes(searchTerm) ||
                                    elementName.toLowerCase().includes(searchTerm) ||
                                    elementPrice.toLowerCase().includes(searchTerm);
                const matchesCompany = !companyFilter || companyName === companyFilter;
                const matchesProduct = !productFilter || elementProduct === productFilter;

                const shouldShow = matchesSearch && matchesCompany && matchesProduct;
                element.style.display = shouldShow ? 'block' : 'none';
            });

            // Hide websites with no visible elements
            websites.forEach(website => {
                const visibleElements = website.querySelectorAll('.element-item[style="display: block"], .element-item:not([style*="display: none"])');
                website.style.display = visibleElements.length > 0 ? 'block' : 'none';
            });

            // Hide companies with no visible websites
            companies.forEach(company => {
                const visibleWebsites = company.querySelectorAll('.element-card[style="display: block"], .element-card:not([style*="display: none"])');
                company.style.display = visibleWebsites.length > 0 ? 'block' : 'none';
            });
        }

        function formatJson(countryFile) {
            const editor = document.getElementById(`editor-${countryFile}`);
            try {
                const parsed = JSON.parse(editor.value);
                editor.value = JSON.stringify(parsed, null, 2);
                showStatus(countryFile, 'JSON formatted successfully', 'success');
            } catch (e) {
                showStatus(countryFile, `Cannot format invalid JSON: ${e.message}`, 'error');
            }
        }

        function syncToVisual(countryFile) {
            const editor = document.getElementById(`editor-${countryFile}`);
            try {
                const parsed = JSON.parse(editor.value);
                configData[countryFile] = parsed;
                generateVisualEditor(countryFile, parsed);
            } catch (e) {
                // Silently fail for invalid JSON during typing
            }
        }

        function syncFromVisual(countryFile) {
            if (configData[countryFile]) {
                const editor = document.getElementById(`editor-${countryFile}`);
                editor.value = JSON.stringify(configData[countryFile], null, 2);
                showStatus(countryFile, 'Synced from visual editor', 'success');
            }
        }

        function addNewElement(countryFile) {
            const modal = createElementModal(countryFile, 'Add New Element', {}, (elementData) => {
                // Add to configData
                const { country, company, website, elementName } = elementData;
                if (!configData[countryFile][country]) configData[countryFile][country] = {};
                if (!configData[countryFile][country][company]) configData[countryFile][country][company] = {};
                if (!configData[countryFile][country][company][website]) {
                    configData[countryFile][country][company][website] = { url: '', Elements: {} };
                }
                if (!configData[countryFile][country][company][website].Elements) {
                    configData[countryFile][country][company][website].Elements = {};
                }

                configData[countryFile][country][company][website].Elements[elementName] = elementData.element;

                // Update displays
                generateVisualEditor(countryFile, configData[countryFile]);
                syncFromVisual(countryFile);
                showStatus(countryFile, 'Element added successfully', 'success');
            });
        }

        function addNewWebsite(countryFile) {
            const modal = createWebsiteModal(countryFile, 'Add New Website', {}, (websiteData) => {
                const { country, company, websiteName, url } = websiteData;
                if (!configData[countryFile][country]) configData[countryFile][country] = {};
                if (!configData[countryFile][country][company]) configData[countryFile][country][company] = {};

                configData[countryFile][country][company][websiteName] = {
                    url: url,
                    Elements: {}
                };

                generateVisualEditor(countryFile, configData[countryFile]);
                syncFromVisual(countryFile);
                showStatus(countryFile, 'Website added successfully', 'success');
            });
        }

        function editElement(countryFile, country, company, website, elementName) {
            const elementData = configData[countryFile][country][company][website].Elements[elementName];
            const modal = createElementModal(countryFile, 'Edit Element', {
                country, company, website, elementName, element: elementData
            }, (newElementData) => {
                // Update configData
                delete configData[countryFile][country][company][website].Elements[elementName];
                configData[countryFile][country][company][website].Elements[newElementData.elementName] = newElementData.element;

                generateVisualEditor(countryFile, configData[countryFile]);
                syncFromVisual(countryFile);
                showStatus(countryFile, 'Element updated successfully', 'success');
            });
        }

        function deleteElement(countryFile, country, company, website, elementName) {
            if (confirm(`Are you sure you want to delete element "${elementName}"?`)) {
                delete configData[countryFile][country][company][website].Elements[elementName];
                generateVisualEditor(countryFile, configData[countryFile]);
                syncFromVisual(countryFile);
                showStatus(countryFile, 'Element deleted successfully', 'success');
            }
        }

        function updateWebsiteUrl(countryFile, country, company, website, newUrl) {
            configData[countryFile][country][company][website].url = newUrl;
            syncFromVisual(countryFile);
        }

        function createElementModal(countryFile, title, initialData, onSave) {
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';

            const countries = Object.keys(configData[countryFile] || {});
            const companies = new Set();
            const websites = new Set();

            // Collect all companies and websites
            Object.values(configData[countryFile] || {}).forEach(countryData => {
                Object.keys(countryData).forEach(company => {
                    companies.add(company);
                    Object.keys(countryData[company]).forEach(website => {
                        websites.add(website);
                    });
                });
            });

            modal.innerHTML = `
                <div class="bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-96 overflow-y-auto">
                    <h3 class="text-lg font-bold mb-4">${title}</h3>
                    <form id="element-form">
                        <div class="grid grid-cols-2 gap-4 mb-4">
                            <div>
                                <label class="block text-sm font-medium mb-1">Country</label>
                                <input type="text" name="country" value="${initialData.country || ''}"
                                       list="countries-list" class="w-full border border-gray-300 rounded px-3 py-2" required>
                                <datalist id="countries-list">
                                    ${countries.map(c => `<option value="${c}">`).join('')}
                                </datalist>
                            </div>
                            <div>
                                <label class="block text-sm font-medium mb-1">Company</label>
                                <input type="text" name="company" value="${initialData.company || ''}"
                                       list="companies-list" class="w-full border border-gray-300 rounded px-3 py-2" required>
                                <datalist id="companies-list">
                                    ${Array.from(companies).map(c => `<option value="${c}">`).join('')}
                                </datalist>
                            </div>
                            <div>
                                <label class="block text-sm font-medium mb-1">Website</label>
                                <input type="text" name="website" value="${initialData.website || ''}"
                                       list="websites-list" class="w-full border border-gray-300 rounded px-3 py-2" required>
                                <datalist id="websites-list">
                                    ${Array.from(websites).map(w => `<option value="${w}">`).join('')}
                                </datalist>
                            </div>
                            <div>
                                <label class="block text-sm font-medium mb-1">Element Name</label>
                                <input type="text" name="elementName" value="${initialData.elementName || ''}"
                                       class="w-full border border-gray-300 rounded px-3 py-2" required>
                            </div>
                        </div>

                        <div class="grid grid-cols-2 gap-4 mb-4">
                            <div>
                                <label class="block text-sm font-medium mb-1">Product</label>
                                <input type="text" name="product" value="${initialData.element?.product || ''}"
                                       class="w-full border border-gray-300 rounded px-3 py-2">
                            </div>
                            <div>
                                <label class="block text-sm font-medium mb-1">UID</label>
                                <input type="text" name="uid" value="${initialData.element?.uid || ''}"
                                       class="w-full border border-gray-300 rounded px-3 py-2">
                            </div>
                            <div>
                                <label class="block text-sm font-medium mb-1">Weight Selector</label>
                                <input type="text" name="weight" value="${initialData.element?.weight || ''}"
                                       class="w-full border border-gray-300 rounded px-3 py-2">
                            </div>
                            <div>
                                <label class="block text-sm font-medium mb-1">Weight Text</label>
                                <input type="text" name="weight_text" value="${initialData.element?.weight_text || ''}"
                                       class="w-full border border-gray-300 rounded px-3 py-2">
                            </div>
                        </div>

                        <div class="mb-4">
                            <label class="block text-sm font-medium mb-1">Selector (name field)</label>
                            <input type="text" name="name" value="${initialData.element?.name || initialData.element?.selector || ''}"
                                   class="w-full border border-gray-300 rounded px-3 py-2">
                        </div>

                        <div class="mb-4">
                            <label class="block text-sm font-medium mb-1">Price Selector</label>
                            <input type="text" name="price" value="${initialData.element?.price || initialData.element?.price_selector || ''}"
                                   class="w-full border border-gray-300 rounded px-3 py-2">
                        </div>

                        <div class="mb-4">
                            <label class="block text-sm font-medium mb-1">Photo Path</label>
                            <input type="text" name="photo" value="${initialData.element?.photo || ''}"
                                   class="w-full border border-gray-300 rounded px-3 py-2">
                        </div>

                        <div class="flex justify-end space-x-3">
                            <button type="button" onclick="this.closest('.fixed').remove()"
                                    class="px-4 py-2 text-gray-600 border border-gray-300 rounded hover:bg-gray-50">
                                Cancel
                            </button>
                            <button type="submit"
                                    class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
                                Save
                            </button>
                        </div>
                    </form>
                </div>
            `;

            modal.querySelector('#element-form').addEventListener('submit', (e) => {
                e.preventDefault();
                const formData = new FormData(e.target);
                const elementData = {
                    country: formData.get('country'),
                    company: formData.get('company'),
                    website: formData.get('website'),
                    elementName: formData.get('elementName'),
                    element: {
                        product: formData.get('product'),
                        uid: formData.get('uid'),
                        weight_selector: formData.get('weight'),
                        weight_text: formData.get('weight_text'),
                        name: formData.get('name'),
                        price: formData.get('price'),
                        photo: formData.get('photo')
                    }
                };

                onSave(elementData);
                modal.remove();
            });

            document.body.appendChild(modal);
            return modal;
        }

        function createWebsiteModal(countryFile, title, initialData, onSave) {
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';

            const countries = Object.keys(configData[countryFile] || {});
            const companies = new Set();

            Object.values(configData[countryFile] || {}).forEach(countryData => {
                Object.keys(countryData).forEach(company => {
                    companies.add(company);
                });
            });

            modal.innerHTML = `
                <div class="bg-white rounded-lg p-6 max-w-lg w-full mx-4">
                    <h3 class="text-lg font-bold mb-4">${title}</h3>
                    <form id="website-form">
                        <div class="mb-4">
                            <label class="block text-sm font-medium mb-1">Country</label>
                            <input type="text" name="country" value="${initialData.country || ''}"
                                   list="countries-list" class="w-full border border-gray-300 rounded px-3 py-2" required>
                            <datalist id="countries-list">
                                ${countries.map(c => `<option value="${c}">`).join('')}
                            </datalist>
                        </div>
                        <div class="mb-4">
                            <label class="block text-sm font-medium mb-1">Company</label>
                            <input type="text" name="company" value="${initialData.company || ''}"
                                   list="companies-list" class="w-full border border-gray-300 rounded px-3 py-2" required>
                            <datalist id="companies-list">
                                ${Array.from(companies).map(c => `<option value="${c}">`).join('')}
                            </datalist>
                        </div>
                        <div class="mb-4">
                            <label class="block text-sm font-medium mb-1">Website Name</label>
                            <input type="text" name="websiteName" value="${initialData.websiteName || ''}"
                                   class="w-full border border-gray-300 rounded px-3 py-2" required>
                        </div>
                        <div class="mb-4">
                            <label class="block text-sm font-medium mb-1">URL</label>
                            <input type="url" name="url" value="${initialData.url || ''}"
                                   class="w-full border border-gray-300 rounded px-3 py-2" required>
                        </div>

                        <div class="flex justify-end space-x-3">
                            <button type="button" onclick="this.closest('.fixed').remove()"
                                    class="px-4 py-2 text-gray-600 border border-gray-300 rounded hover:bg-gray-50">
                                Cancel
                            </button>
                            <button type="submit"
                                    class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
                                Save
                            </button>
                        </div>
                    </form>
                </div>
            `;

            modal.querySelector('#website-form').addEventListener('submit', (e) => {
                e.preventDefault();
                const formData = new FormData(e.target);
                const websiteData = {
                    country: formData.get('country'),
                    company: formData.get('company'),
                    websiteName: formData.get('websiteName'),
                    url: formData.get('url')
                };

                onSave(websiteData);
                modal.remove();
            });

            document.body.appendChild(modal);
            return modal;
        }

        function deleteWebsite(countryFile, country, company, website) {
            if (confirm(`Are you sure you want to delete website "${website}" and all its elements?`)) {
                delete configData[countryFile][country][company][website];
                generateVisualEditor(countryFile, configData[countryFile]);
                syncFromVisual(countryFile);
                showStatus(countryFile, 'Website deleted successfully', 'success');
            }
        }

        function deleteCompany(countryFile, country, company) {
            if (confirm(`Are you sure you want to delete company "${company}" and all its websites?`)) {
                delete configData[countryFile][country][company];
                generateVisualEditor(countryFile, configData[countryFile]);
                syncFromVisual(countryFile);
                showStatus(countryFile, 'Company deleted successfully', 'success');
            }
        }

        function deleteCountry(countryFile, country) {
            if (confirm(`Are you sure you want to delete country "${country}" and all its data?`)) {
                delete configData[countryFile][country];
                generateVisualEditor(countryFile, configData[countryFile]);
                syncFromVisual(countryFile);
                showStatus(countryFile, 'Country deleted successfully', 'success');
            }
        }

        function editWebsite(countryFile, country, company, website) {
            const websiteData = configData[countryFile][country][company][website];
            const modal = createWebsiteModal(countryFile, 'Edit Website', {
                country, company, websiteName: website, url: websiteData.url
            }, (newWebsiteData) => {
                // If website name changed, move the data
                if (newWebsiteData.websiteName !== website) {
                    configData[countryFile][country][company][newWebsiteData.websiteName] = websiteData;
                    delete configData[countryFile][country][company][website];
                }

                // Update URL
                configData[countryFile][country][company][newWebsiteData.websiteName].url = newWebsiteData.url;

                generateVisualEditor(countryFile, configData[countryFile]);
                syncFromVisual(countryFile);
                showStatus(countryFile, 'Website updated successfully', 'success');
            });
        }

        function addElementToWebsite(countryFile, country, company, website) {
            const modal = createElementModal(countryFile, 'Add Element to Website', {
                country, company, website
            }, (elementData) => {
                configData[countryFile][country][company][website].Elements[elementData.elementName] = elementData.element;
                generateVisualEditor(countryFile, configData[countryFile]);
                syncFromVisual(countryFile);
                showStatus(countryFile, 'Element added to website successfully', 'success');
            });
        }
    </script>
</body>
</html>
