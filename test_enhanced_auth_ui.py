#!/usr/bin/env python3
"""
Test script for enhanced authentication UI functionality.

This script tests:
1. Authentication status display on all protected pages
2. Logout functionality across all pages
3. Visual indicators and user experience
4. Unified authentication system integration
"""

import requests
import sys
from urllib.parse import urljoin
import re

# Configuration
BASE_URL = "http://localhost:8000"
ADMIN_USERNAME = "admin"
ADMIN_PASSWORD = "adminrubis2025"

def test_auth_ui():
    """Test the enhanced authentication UI across all protected pages"""
    
    print("=== Testing Enhanced Authentication UI ===")
    
    # Create a session to maintain cookies
    session = requests.Session()
    
    # Test pages to check
    protected_pages = [
        ("/manual-request", "Manual Request"),
        ("/admin", "Admin Panel"),
        ("/admin/sites", "Site Configuration Manager")
    ]
    
    print("\n1. Testing authentication flow and UI elements...")
    
    # Step 1: Test login and get auth cookie
    print("   → Logging in...")
    login_response = session.post(
        urljoin(BASE_URL, "/auth/login"),
        data={"username": ADMIN_USERNAME, "password": ADMIN_PASSWORD},
        allow_redirects=False
    )
    
    if login_response.status_code != 302:
        print(f"   ❌ Login failed with status {login_response.status_code}")
        return False
    
    # Check if auth cookie was set
    auth_cookie = session.cookies.get("auth_credentials")
    if not auth_cookie:
        print("   ❌ Auth cookie not set after login")
        return False
    
    print(f"   ✅ Login successful, auth cookie set")
    
    # Step 2: Test each protected page for authentication UI elements
    for page_url, page_name in protected_pages:
        print(f"\n2. Testing {page_name} ({page_url})...")
        
        response = session.get(urljoin(BASE_URL, page_url))
        
        if response.status_code != 200:
            print(f"   ❌ Failed to access {page_name}: {response.status_code}")
            continue
        
        content = response.text
        
        # Check for authentication header component
        if 'auth_header.html' in content or 'Authentication Header Component' in content:
            print(f"   ✅ Authentication header component included")
        else:
            print(f"   ❌ Authentication header component missing")
        
        # Check for authentication status indicators
        auth_indicators = [
            ('Username display', ADMIN_USERNAME),
            ('Authenticated status', 'Authenticated'),
            ('Green status indicator', 'bg-green-'),
            ('User icon', 'svg'),
            ('Logout button', 'Logout')
        ]
        
        for indicator_name, search_term in auth_indicators:
            if search_term in content:
                print(f"   ✅ {indicator_name} found")
            else:
                print(f"   ❌ {indicator_name} missing")
        
        # Check for logout form
        if 'action="/auth/logout"' in content:
            print(f"   ✅ Logout form found")
        else:
            print(f"   ❌ Logout form missing")
        
        # Check for navigation links
        nav_links = [
            ('/manual-request', 'Manual Request'),
            ('/admin', 'Admin'),
            ('/results', 'Results'),
            ('/database', 'Database')
        ]
        
        nav_found = 0
        for link_url, link_name in nav_links:
            if link_url in content:
                nav_found += 1
        
        if nav_found >= 3:  # Should have most navigation links
            print(f"   ✅ Navigation links found ({nav_found}/{len(nav_links)})")
        else:
            print(f"   ❌ Navigation links missing ({nav_found}/{len(nav_links)})")
    
    # Step 3: Test logout functionality
    print(f"\n3. Testing logout functionality...")
    
    logout_response = session.post(
        urljoin(BASE_URL, "/auth/logout"),
        allow_redirects=False
    )
    
    if logout_response.status_code == 302:
        print(f"   ✅ Logout redirect successful")
        
        # Check if redirected to login page
        location = logout_response.headers.get('Location', '')
        if '/login' in location:
            print(f"   ✅ Redirected to login page")
        else:
            print(f"   ❌ Not redirected to login page: {location}")
        
        # Check if auth cookie was cleared
        auth_cookie_after_logout = session.cookies.get("auth_credentials")
        if not auth_cookie_after_logout:
            print(f"   ✅ Auth cookie cleared after logout")
        else:
            print(f"   ❌ Auth cookie not cleared after logout")
    else:
        print(f"   ❌ Logout failed with status {logout_response.status_code}")
    
    # Step 4: Test access to protected pages after logout
    print(f"\n4. Testing access protection after logout...")
    
    for page_url, page_name in protected_pages:
        response = session.get(urljoin(BASE_URL, page_url), allow_redirects=False)
        
        if response.status_code == 302:
            location = response.headers.get('Location', '')
            if '/login' in location:
                print(f"   ✅ {page_name} properly redirects to login after logout")
            else:
                print(f"   ❌ {page_name} redirects to wrong location: {location}")
        else:
            print(f"   ❌ {page_name} doesn't redirect after logout: {response.status_code}")
    
    # Step 5: Test visual consistency
    print(f"\n5. Testing visual consistency...")
    
    # Re-login for visual tests
    session.post(
        urljoin(BASE_URL, "/auth/login"),
        data={"username": ADMIN_USERNAME, "password": ADMIN_PASSWORD}
    )
    
    # Check that all pages have consistent styling
    for page_url, page_name in protected_pages:
        response = session.get(urljoin(BASE_URL, page_url))
        content = response.text
        
        # Check for Tailwind CSS
        if 'tailwindcss.com' in content:
            print(f"   ✅ {page_name} has Tailwind CSS")
        else:
            print(f"   ❌ {page_name} missing Tailwind CSS")
        
        # Check for consistent header styling
        styling_elements = [
            'bg-white border-b border-gray-200',  # Header background
            'text-blue-600',  # Brand color
            'bg-green-50',    # Auth status background
            'bg-red-50'       # Logout button background
        ]
        
        styling_found = sum(1 for element in styling_elements if element in content)
        if styling_found >= 2:
            print(f"   ✅ {page_name} has consistent styling ({styling_found}/{len(styling_elements)})")
        else:
            print(f"   ❌ {page_name} missing consistent styling ({styling_found}/{len(styling_elements)})")
    
    print(f"\n=== Enhanced Authentication UI Test Completed ===")
    return True

def test_mobile_responsiveness():
    """Test mobile responsiveness of authentication UI"""
    print(f"\n=== Testing Mobile Responsiveness ===")
    
    session = requests.Session()
    
    # Login first
    session.post(
        urljoin(BASE_URL, "/auth/login"),
        data={"username": ADMIN_USERNAME, "password": ADMIN_PASSWORD}
    )
    
    # Test manual-request page for mobile elements
    response = session.get(urljoin(BASE_URL, "/manual-request"))
    content = response.text
    
    mobile_elements = [
        ('Mobile menu button', 'md:hidden'),
        ('Mobile navigation', 'mobile-menu'),
        ('Responsive grid', 'md:flex'),
        ('Hidden on mobile', 'hidden md:flex')
    ]
    
    for element_name, search_term in mobile_elements:
        if search_term in content:
            print(f"   ✅ {element_name} found")
        else:
            print(f"   ❌ {element_name} missing")

if __name__ == "__main__":
    try:
        success = test_auth_ui()
        test_mobile_responsiveness()
        
        if success:
            print(f"\n🎉 All authentication UI tests completed!")
            sys.exit(0)
        else:
            print(f"\n❌ Some tests failed!")
            sys.exit(1)
            
    except requests.exceptions.ConnectionError:
        print(f"❌ Could not connect to {BASE_URL}")
        print("Make sure the server is running with: uvicorn app.main:app --reload")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        sys.exit(1)
