#!/usr/bin/env python3
"""
Test logout functionality with browser-like behavior using selenium-like approach.
"""

import requests
from urllib.parse import urljoin
from bs4 import BeautifulSoup
import re

BASE_URL = "http://localhost:8000"
ADMIN_USERNAME = "admin"
ADMIN_PASSWORD = "adminrubis2025"

def test_logout_form_submission():
    """Test logout form submission like a browser would"""
    
    print("=== Testing Logout Form Submission ===")
    
    session = requests.Session()
    
    # Step 1: Login
    print("1. Logging in...")
    login_response = session.post(
        urljoin(BASE_URL, "/auth/login"),
        data={"username": ADMIN_USERNAME, "password": ADMIN_PASSWORD},
        allow_redirects=True  # Follow redirects like a browser
    )
    
    if login_response.status_code != 200:
        print(f"   ❌ Login failed: {login_response.status_code}")
        return False
    
    print("   ✅ Login successful")
    
    # Step 2: Get the manual request page and parse the logout form
    print("2. Getting manual request page and parsing logout form...")
    page_response = session.get(urljoin(BASE_URL, "/manual-request"))
    
    if page_response.status_code != 200:
        print(f"   ❌ Cannot access manual request page: {page_response.status_code}")
        return False
    
    # Parse the HTML to find the logout form
    soup = BeautifulSoup(page_response.text, 'html.parser')
    logout_form = soup.find('form', {'action': '/auth/logout'})
    
    if not logout_form:
        print("   ❌ Logout form not found in page")
        return False
    
    print("   ✅ Logout form found")
    
    # Check if the form has the correct method
    form_method = logout_form.get('method', '').lower()
    print(f"   Form method: {form_method}")
    
    if form_method != 'post':
        print(f"   ❌ Form method should be POST, found: {form_method}")
        return False
    
    # Step 3: Submit the logout form
    print("3. Submitting logout form...")
    logout_response = session.post(
        urljoin(BASE_URL, "/auth/logout"),
        allow_redirects=False  # Don't follow redirects to see the response
    )
    
    print(f"   Logout response status: {logout_response.status_code}")
    print(f"   Logout response headers: {dict(logout_response.headers)}")
    
    if logout_response.status_code == 302:
        location = logout_response.headers.get('Location', '')
        print(f"   ✅ Logout redirect to: {location}")
        
        # Follow the redirect manually
        print("4. Following logout redirect...")
        redirect_response = session.get(urljoin(BASE_URL, location))
        print(f"   Redirect response status: {redirect_response.status_code}")
        
        if redirect_response.status_code == 200:
            print("   ✅ Successfully redirected to login page")
            
            # Check if we're actually on the login page
            if 'login' in redirect_response.text.lower() and 'username' in redirect_response.text.lower():
                print("   ✅ Confirmed on login page")
            else:
                print("   ❌ Not on login page after redirect")
        else:
            print(f"   ❌ Redirect failed: {redirect_response.status_code}")
    else:
        print(f"   ❌ Logout failed: {logout_response.status_code}")
        print(f"   Response content: {logout_response.text}")
    
    # Step 4: Verify authentication is cleared
    print("5. Verifying authentication is cleared...")
    test_protected = session.get(urljoin(BASE_URL, "/manual-request"), allow_redirects=False)
    
    if test_protected.status_code == 302:
        print("   ✅ Authentication cleared - redirected to login")
    else:
        print(f"   ❌ Authentication not cleared - status: {test_protected.status_code}")
    
    return True

def test_javascript_issues():
    """Check for potential JavaScript issues in the logout form"""
    
    print("\n=== Checking for JavaScript Issues ===")
    
    session = requests.Session()
    
    # Login
    session.post(
        urljoin(BASE_URL, "/auth/login"),
        data={"username": ADMIN_USERNAME, "password": ADMIN_PASSWORD}
    )
    
    # Get the page content
    page_response = session.get(urljoin(BASE_URL, "/manual-request"))
    page_content = page_response.text
    
    # Check for JavaScript errors or issues
    print("1. Checking for JavaScript in auth header...")
    
    if 'addEventListener' in page_content:
        print("   ✅ JavaScript event listeners found")
    else:
        print("   ❌ No JavaScript event listeners found")
    
    if 'DOMContentLoaded' in page_content:
        print("   ✅ DOMContentLoaded event found")
    else:
        print("   ❌ DOMContentLoaded event not found")
    
    # Check for form submission handling
    if 'form[action="/auth/logout"]' in page_content:
        print("   ✅ Logout form selector found in JavaScript")
    else:
        print("   ❌ Logout form selector not found in JavaScript")
    
    # Check for potential conflicts
    script_tags = page_content.count('<script>')
    print(f"   Number of script tags: {script_tags}")
    
    # Look for HTMX or other libraries that might interfere
    if 'htmx' in page_content.lower():
        print("   ⚠️  HTMX found - potential form submission conflict")
    else:
        print("   ✅ No HTMX conflicts detected")
    
    # Check for multiple forms with same action
    logout_form_count = page_content.count('action="/auth/logout"')
    print(f"   Number of logout forms: {logout_form_count}")
    
    if logout_form_count > 1:
        print("   ⚠️  Multiple logout forms found - potential conflict")
    else:
        print("   ✅ Single logout form found")

def test_csrf_or_security_issues():
    """Check for CSRF or other security-related issues"""
    
    print("\n=== Checking for Security Issues ===")
    
    session = requests.Session()
    
    # Try logout without being logged in
    print("1. Testing logout without authentication...")
    logout_response = session.post(urljoin(BASE_URL, "/auth/logout"), allow_redirects=False)
    print(f"   Status: {logout_response.status_code}")
    
    if logout_response.status_code == 302:
        print("   ✅ Logout works without authentication (redirects)")
    else:
        print(f"   ❌ Unexpected response: {logout_response.status_code}")
    
    # Check for CSRF tokens
    session.post(
        urljoin(BASE_URL, "/auth/login"),
        data={"username": ADMIN_USERNAME, "password": ADMIN_PASSWORD}
    )
    
    page_response = session.get(urljoin(BASE_URL, "/manual-request"))
    
    if 'csrf' in page_response.text.lower() or '_token' in page_response.text.lower():
        print("2. CSRF tokens found - may need to include in logout")
    else:
        print("2. No CSRF tokens detected")

if __name__ == "__main__":
    try:
        test_logout_form_submission()
        test_javascript_issues()
        test_csrf_or_security_issues()
    except requests.exceptions.ConnectionError:
        print(f"❌ Could not connect to {BASE_URL}")
        print("Make sure the server is running")
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
