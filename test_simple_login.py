#!/usr/bin/env python3
"""
Simple test to verify login credentials and authentication flow.
"""

import requests
from urllib.parse import urljoin

BASE_URL = "http://localhost:8000"

def test_simple_login():
    """Test basic login functionality"""
    
    print("=== Testing Simple Login ===")
    
    session = requests.Session()
    
    # Test 1: Get login page
    print("1. Getting login page...")
    login_page = session.get(urljoin(BASE_URL, "/login"))
    print(f"   Status: {login_page.status_code}")
    if "login" in login_page.text.lower():
        print("   ✅ Login page loaded")
    else:
        print("   ❌ Login page not found")
    
    # Test 2: Try login with correct credentials
    print("2. Testing login with admin/adminrubis2025...")
    login_response = session.post(
        urljoin(BASE_URL, "/auth/login"),
        data={"username": "admin", "password": "adminrubis2025"},
        allow_redirects=False
    )
    print(f"   Status: {login_response.status_code}")
    print(f"   Headers: {dict(login_response.headers)}")
    
    if login_response.status_code == 302:
        print("   ✅ Login successful (redirect)")
        location = login_response.headers.get('Location', '')
        print(f"   Redirect to: {location}")
    else:
        print(f"   ❌ Login failed")
        print(f"   Response: {login_response.text[:200]}")
    
    # Test 3: Check if auth cookie was set
    auth_cookie = session.cookies.get("auth_credentials")
    if auth_cookie:
        print(f"   ✅ Auth cookie set: {auth_cookie[:20]}...")
    else:
        print("   ❌ Auth cookie not set")
    
    # Test 4: Try accessing protected page
    print("3. Testing access to protected page...")
    manual_request = session.get(urljoin(BASE_URL, "/manual-request"))
    print(f"   Status: {manual_request.status_code}")
    
    if manual_request.status_code == 200:
        print("   ✅ Protected page accessible")
        if "admin" in manual_request.text:
            print("   ✅ Username found in page")
        else:
            print("   ❌ Username not found in page")
    else:
        print(f"   ❌ Protected page not accessible")

if __name__ == "__main__":
    try:
        test_simple_login()
    except requests.exceptions.ConnectionError:
        print(f"❌ Could not connect to {BASE_URL}")
        print("Make sure the server is running")
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
