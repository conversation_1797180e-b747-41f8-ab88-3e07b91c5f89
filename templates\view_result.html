<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ filename }} - <PERSON> Price Scraper</title>
    <link rel="icon" href="/static/media/favicon.ico" type="image/x-icon">
    <script src="https://unpkg.com/htmx.org@2.0.4" integrity="sha384-HGfztofotfshcF7+8n44JQL2oJmowVChPTg48S+jvZoztPfvwD79OC/LTtG6dMp+" crossorigin="anonymous"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <!-- Authentication Header -->
        {% include "auth_header.html" %}

        <header class="mb-8">
            <h1 class="text-4xl font-bold text-center text-blue-600">Gas Bottle Price Scraper</h1>
            <p class="text-center text-gray-600 mt-2">Viewing results: {{ filename }}</p>
        </header>

        <nav class="flex justify-center space-x-6 mb-8">
            <a href="/database" class="text-blue-600 font-medium hover:text-blue-800">Database View</a>
            <a href="/results" class="text-blue-600 font-medium hover:text-blue-800">View All Results</a>
        </nav>

        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-2xl font-semibold">Result Summary</h2>
                <a href="/static/results/{{ filename }}" download class="bg-green-600 hover:bg-green-700 text-white font-medium py-1 px-3 rounded-md transition duration-300">
                    Download CSV
                </a>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div class="bg-gray-50 p-4 rounded-lg">
                    <p class="text-gray-600">Total Items</p>
                    <p class="text-2xl font-bold">{{ summary.total_items }}</p>
                </div>
                <div class="bg-gray-50 p-4 rounded-lg">
                    <p class="text-gray-600">Countries</p>
                    <p class="text-2xl font-bold">{{ summary.countries }}</p>
                </div>
                <div class="bg-gray-50 p-4 rounded-lg">
                    <p class="text-gray-600">Companies</p>
                    <p class="text-2xl font-bold">{{ summary.companies }}</p>
                </div>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="bg-gray-50 p-4 rounded-lg">
                    <p class="text-gray-600">Price Range</p>
                    <p class="text-xl font-bold">{{ summary.price_range }}</p>
                </div>
                <div class="bg-gray-50 p-4 rounded-lg">
                    <p class="text-gray-600">Average Price</p>
                    <p class="text-xl font-bold">{{ summary.avg_price }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6">
            <h2 class="text-2xl font-semibold mb-4">Detailed Results</h2>
            
            <div class="mb-6">
                <div class="flex mb-4">
                    <input id="searchInput" type="text" placeholder="Search by country, company or product..."
                           class="border border-gray-300 rounded-md px-4 py-2 w-full focus:outline-none focus:ring-2 focus:ring-blue-600"
                           onkeyup="applyFilters()">
                    <span id="search-indicator" class="htmx-indicator ml-2">
                        <svg class="animate-spin h-5 w-5 text-blue-600 inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Searching...
                    </span>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                        <label for="countryFilter" class="block text-sm font-medium text-gray-700 mb-1">Country</label>
                        <select id="countryFilter" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-600" onchange="applyFilters()">
                            <option value="">All Countries</option>
                            {% for country in countries %}
                            <option value="{{ country }}">{{ country }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div>
                        <label for="companyFilter" class="block text-sm font-medium text-gray-700 mb-1">Company</label>
                        <select id="companyFilter" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-600" onchange="applyFilters()">
                            <option value="">All Companies</option>
                            {% for company in companies %}
                            <option value="{{ company }}">{{ company }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div>
                        <label for="typeFilter" class="block text-sm font-medium text-gray-700 mb-1">Product Type</label>
                        <select id="typeFilter" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-600" onchange="applyFilters()">
                            <option value="">All Types</option>
                            {% for type in product_types %}
                            <option value="{{ type }}">{{ type }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div>
                        <label for="weightFilter" class="block text-sm font-medium text-gray-700 mb-1">Weight (kg)</label>
                        <select id="weightFilter" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-600" onchange="applyFilters()">
                            <option value="">Any Weight</option>
                            <option value="0-5">0 - 5 kg</option>
                            <option value="5-10">5 - 10 kg</option>
                            <option value="10-15">10 - 15 kg</option>
                            <option value="15+">15+ kg</option>
                        </select>
                    </div>
                </div>
                
                <div class="mt-4">
                    <div class="flex justify-between items-center mb-2">
                        <label class="block text-sm font-medium text-gray-700">Price Range (€)</label>
                        <span id="priceRangeValue" class="text-sm text-gray-600">0 - 100€</span>
                    </div>
                    <div class="relative h-2 bg-gray-200 rounded-lg">
                        <input type="range" id="minPriceRange" min="0" max="100" step="1" value="0"
                               class="absolute w-full h-2 bg-transparent appearance-none cursor-pointer z-10"
                               oninput="updateDualPriceRange()" onchange="applyFilters()">
                               
                        <input type="range" id="maxPriceRange" min="0" max="100" step="1" value="100"
                               class="absolute w-full h-2 bg-transparent appearance-none cursor-pointer z-20"
                               oninput="updateDualPriceRange()" onchange="applyFilters()">
                               
                        <div id="priceRangeTrack" class="absolute h-2 bg-blue-500 rounded-lg"></div>
                    </div>
                    <div class="flex justify-between mt-2">
                        <input type="number" id="minPriceInput" min="0" class="w-20 border border-gray-300 rounded-md px-2 py-1 text-sm"
                               oninput="updateRangeFromInput()" onchange="applyFilters()">
                        <input type="number" id="maxPriceInput" min="0" class="w-20 border border-gray-300 rounded-md px-2 py-1 text-sm"
                               oninput="updateRangeFromInput()" onchange="applyFilters()">
                    </div>
                </div>
                
                <div class="flex justify-end mt-4 space-x-2">
                    <button onclick="resetFilters()" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-medium py-1 px-3 rounded-md transition duration-300">
                        Reset Filters
                    </button>
                    </div>
            </div>
            
            <div class="mb-4 text-gray-600">
                <span id="resultsCounter">Showing all items</span>
            </div>

            <div class="mb-8">
                <h3 class="text-xl font-semibold mb-4">Price vs Weight Scatter Plot</h3>
                <div class="flex justify-center relative h-96 w-1/2">
                    <canvas id="priceWeightChart" height="100"></canvas>
                </div>
            </div>

            <div class="overflow-x-auto">
                <table id="resultsTable" class="min-w-full bg-white">
                    <thead>
                        <tr>
                            <th class="py-3 px-4 text-left border-b-2 border-gray-200 bg-gray-100 text-gray-600">Image</th>
                            <th class="py-3 px-4 text-left border-b-2 border-gray-200 bg-gray-100 text-gray-600">Country</th>
                            <th class="py-3 px-4 text-left border-b-2 border-gray-200 bg-gray-100 text-gray-600">Company</th>
                            <th class="py-3 px-4 text-left border-b-2 border-gray-200 bg-gray-100 text-gray-600">Product</th>
                            <th class="py-3 px-4 text-left border-b-2 border-gray-200 bg-gray-100 text-gray-600">Type</th>
                            <th class="py-3 px-4 text-left border-b-2 border-gray-200 bg-gray-100 text-gray-600">Name</th>
                            <th class="py-3 px-4 text-right border-b-2 border-gray-200 bg-gray-100 text-gray-600">Weight (kg)</th>
                            <th class="py-3 px-4 text-right border-b-2 border-gray-200 bg-gray-100 text-gray-600">Price (€)</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for record in records %}
                        <tr class="hover:bg-gray-50">
                            <td class="py-3 px-4 border-b border-gray-200">
                                <img src="{{ record.photo.replace('/workspaces/codespaces-blank/static', '/static') }}"
                                     alt="Product image for {{ record.comercial_name }}"
                                     class="w-20 h-20 object-contain border border-gray-200"
                                     onerror="this.src='/static/media/placeholder.jpg'; this.onerror=null;">
                            </td>
                            <td class="py-3 px-4 border-b border-gray-200">{{ record.country }}</td>
                            <td class="py-3 px-4 border-b border-gray-200">{{ record.company }}</td>
                            <td class="py-3 px-4 border-b border-gray-200">{{ record.product }}</td>
                            <td class="py-3 px-4 border-b border-gray-200">{{ record.product_type }}</td>
                            <td class="py-3 px-4 border-b border-gray-200">{{ record.comercial_name }}</td>
                            <td class="py-3 px-4 border-b border-gray-200 text-right">{{ "%.1f"|format(record.weight) }}</td>
                            <td class="py-3 px-4 border-b border-gray-200 text-right">{{ "%.2f"|format(record.price) }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    
    <div id="job-indicator-container" hx-get="/job-indicator" hx-trigger="load"></div>
    
    <script>
        let priceWeightChart = null; // Variable to hold the chart instance

        // Get the maximum price from records for the price slider setup
        const maxPrice = Math.max(...Array.from(document.querySelectorAll('#resultsTable tbody tr')).map(row => {
            const priceCell = row.cells[7].textContent;
            return parseFloat(priceCell) || 0; // Ensure valid number
        }));

        function initializePriceSliders() {
            const roundedMaxPrice = Math.ceil(maxPrice) || 100; // Default max if no price found
            
            document.getElementById('minPriceRange').max = roundedMaxPrice;
            document.getElementById('maxPriceRange').max = roundedMaxPrice;
            document.getElementById('maxPriceRange').value = roundedMaxPrice;
            document.getElementById('minPriceInput').value = 0;
            document.getElementById('maxPriceInput').value = roundedMaxPrice;
            document.getElementById('minPriceInput').max = roundedMaxPrice; // Set max for number inputs too
            document.getElementById('maxPriceInput').max = roundedMaxPrice;
            
            updateDualPriceRange();
        }

        function updateDualPriceRange() {
            const minSlider = document.getElementById('minPriceRange');
            const maxSlider = document.getElementById('maxPriceRange');
            const minInput = document.getElementById('minPriceInput');
            const maxInput = document.getElementById('maxPriceInput');
            const priceRangeValue = document.getElementById('priceRangeValue');
            const track = document.getElementById('priceRangeTrack');
            
            let minValue = parseFloat(minSlider.value);
            let maxValue = parseFloat(maxSlider.value);
            const sliderMax = parseFloat(minSlider.max); // Use the slider's max value

            // Ensure min doesn't exceed max
            if (minValue > maxValue) {
                minSlider.value = maxValue;
                minValue = maxValue; // Update minValue after correction
            }
            
            // Ensure max doesn't go below min
            if (maxValue < minValue) {
                maxSlider.value = minValue;
                maxValue = minValue; // Update maxValue after correction
            }

            // Update input fields
            minInput.value = minValue;
            maxInput.value = maxValue;
            
            // Update the text display
            priceRangeValue.textContent = `${minValue.toFixed(0)}€ - ${maxValue.toFixed(0)}€`;
            
            // Update the colored track
            const percentage1 = (minValue / sliderMax) * 100;
            const percentage2 = (maxValue / sliderMax) * 100;
            track.style.left = `${percentage1}%`;
            track.style.width = `${percentage2 - percentage1}%`;
        }

        function updateRangeFromInput() {
            const minInput = document.getElementById('minPriceInput');
            const maxInput = document.getElementById('maxPriceInput');
            const minSlider = document.getElementById('minPriceRange');
            const maxSlider = document.getElementById('maxPriceRange');
            
            let minValue = parseFloat(minInput.value);
            let maxValue = parseFloat(maxInput.value);
            const sliderMax = parseFloat(minSlider.max);

            // Validate inputs and clamp to slider bounds
            if (isNaN(minValue) || minValue < 0) minValue = 0;
            if (minValue > sliderMax) minValue = sliderMax;
            if (isNaN(maxValue) || maxValue < 0) maxValue = 0; // Should not happen if min is 0
            if (maxValue > sliderMax) maxValue = sliderMax;
            
            // Ensure min doesn't exceed max
            if (minValue > maxValue) {
                minValue = maxValue;
                minInput.value = minValue; // Update input if corrected
            }

            // Ensure max doesn't go below min
             if (maxValue < minValue) {
                maxValue = minValue;
                maxInput.value = maxValue; // Update input if corrected
            }
            
            // Update slider positions
            minSlider.value = minValue;
            maxSlider.value = maxValue;
            
            // Update the visual elements
            updateDualPriceRange();
        }

        function applyFilters() {
            const searchInput = document.getElementById('searchInput').value.toLowerCase();
            const countryFilter = document.getElementById('countryFilter').value;
            const companyFilter = document.getElementById('companyFilter').value;
            const typeFilter = document.getElementById('typeFilter').value;
            const weightFilter = document.getElementById('weightFilter').value;
            const minPrice = parseFloat(document.getElementById('minPriceRange').value);
            const maxPrice = parseFloat(document.getElementById('maxPriceRange').value);
            
            const rows = document.querySelectorAll('#resultsTable tbody tr');
            let visibleCount = 0;
            
            rows.forEach(row => {
                const cells = row.getElementsByTagName('td');
                const country = cells[1].textContent;
                const company = cells[2].textContent;
                const product = cells[3].textContent; // Include product in search
                const type = cells[4].textContent;
                const name = cells[5].textContent; // Include name in search
                const weight = parseFloat(cells[6].textContent);
                const price = parseFloat(cells[7].textContent);
                
                // Weight match logic
                let weightMatch = true;
                if (weightFilter) {
                    if (weightFilter.includes('-')) {
                        const [minWeight, maxWeight] = weightFilter.split('-');
                        weightMatch = weight >= parseFloat(minWeight) && weight < parseFloat(maxWeight);
                    } else { // For "15+" case
                        weightMatch = weight >= parseFloat(weightFilter.replace('+', ''));
                    }
                }
                
                // Text search logic (checking relevant text columns)
                const textContent = `${country} ${company} ${product} ${name}`.toLowerCase();
                const matchesSearch = !searchInput || textContent.includes(searchInput);
                
                const matchesCountry = !countryFilter || country === countryFilter;
                const matchesCompany = !companyFilter || company === companyFilter;
                const matchesType = !typeFilter || type === typeFilter;
                const matchesPrice = price >= minPrice && price <= maxPrice;
                
                const isVisible = matchesSearch && matchesCountry && matchesCompany && 
                                matchesType && weightMatch && matchesPrice;
                
                row.style.display = isVisible ? '' : 'none';
                if (isVisible) visibleCount++;
            });
            
            // Update results counter
            document.getElementById('resultsCounter').textContent = 
                `Showing ${visibleCount} out of ${rows.length} items`;

            // Update the scatter plot
            createOrUpdateChart();
        }

        function resetFilters() {
            document.getElementById('searchInput').value = '';
            document.getElementById('countryFilter').value = '';
            document.getElementById('companyFilter').value = '';
            document.getElementById('typeFilter').value = '';
            document.getElementById('weightFilter').value = '';
            
            // Reset price range sliders and inputs to full range
            const maxSliderValue = document.getElementById('maxPriceRange').max;
            document.getElementById('minPriceRange').value = 0;
            document.getElementById('maxPriceRange').value = maxSliderValue;
            document.getElementById('minPriceInput').value = 0;
            document.getElementById('maxPriceInput').value = maxSliderValue;
            updateDualPriceRange(); // Update visuals after resetting values
            
            applyFilters(); // Re-apply filters to show all data and update chart
        }

        function createOrUpdateChart() {
            const ctx = document.getElementById('priceWeightChart').getContext('2d');
            const visibleRows = Array.from(document.querySelectorAll('#resultsTable tbody tr'))
                                   .filter(row => row.style.display !== 'none');

            // Get unique countries and create color map
            const countries = [...new Set(visibleRows.map(row => row.getElementsByTagName('td')[1].textContent))];
            const colorMap = {};
            
            // Generate colors for each country
            const colors = [
                'rgba(54, 162, 235, 0.6)',   // blue
                'rgba(255, 99, 132, 0.6)',   // red
                'rgba(75, 192, 192, 0.6)',   // teal
                'rgba(255, 206, 86, 0.6)',   // yellow
                'rgba(153, 102, 255, 0.6)',  // purple
                'rgba(255, 159, 64, 0.6)',   // orange
                'rgba(46, 204, 113, 0.6)',   // green
                'rgba(142, 68, 173, 0.6)',   // violet
                'rgba(52, 152, 219, 0.6)',   // light blue
                'rgba(231, 76, 60, 0.6)',    // dark red
            ];

            countries.forEach((country, index) => {
                colorMap[country] = colors[index % colors.length];
            });

            // Group data points by country
            const datasets = countries.map(country => {
                const countryData = visibleRows
                    .filter(row => row.getElementsByTagName('td')[1].textContent === country)
                    .map(row => {
                        const cells = row.getElementsByTagName('td');
                        return {
                            x: parseFloat(cells[6].textContent), // Weight
                            y: parseFloat(cells[7].textContent), // Price
                            label: `${cells[5].textContent} (${cells[2].textContent})` // Name (Company) for tooltip
                        };
                    });

                return {
                    label: country,
                    data: countryData,
                    backgroundColor: colorMap[country],
                    borderColor: colorMap[country].replace('0.6', '1'), // Solid border
                    pointRadius: 5,
                    pointHoverRadius: 7
                };
            });

            const chartData = {
                datasets: datasets
            };

            const chartOptions = {
                scales: {
                    x: {
                        type: 'linear',
                        position: 'bottom',
                        title: {
                            display: true,
                            text: 'Weight (kg)'
                        }
                    },
                    y: {
                        title: {
                            display: true,
                            text: 'Price (€)'
                        },
                        beginAtZero: true
                    }
                },
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const point = context.raw;
                                return `${context.dataset.label}: ${point.label}: ${point.y.toFixed(2)}€ @ ${point.x.toFixed(1)}kg`;
                            }
                        }
                    },
                    legend: {
                        display: true, // Show legend to display country colors
                        position: 'top',
                        labels: {
                            usePointStyle: true // Makes legend markers round like the points
                        }
                    }
                },
                responsive: true,
                maintainAspectRatio: false
            };

            if (priceWeightChart) {
                priceWeightChart.data = chartData;
                priceWeightChart.options = chartOptions;
                priceWeightChart.update();
            } else {
                priceWeightChart = new Chart(ctx, {
                    type: 'scatter',
                    data: chartData,
                    options: chartOptions
                });
            }
        }

        document.addEventListener('DOMContentLoaded', () => {
            initializePriceSliders();
            populateFilterOptions(); // Keep this if needed, otherwise remove
            applyFilters(); // Apply filters on load to show initial data and chart
        });

        // --- Optional: Keep populateFilterOptions if needed ---
        function populateFilterOptions() {
             // This check prevents re-populating if server already did
            if (document.getElementById('countryFilter').options.length > 1) {
                return; 
            }

            const rows = document.querySelectorAll('#resultsTable tbody tr');
            const countries = new Set();
            const companies = new Set();
            const types = new Set();
            
            rows.forEach(row => {
                if (row.style.display !== 'none') { // Only consider initially visible rows if necessary
                    const cells = row.getElementsByTagName('td');
                    countries.add(cells[1].textContent.trim());
                    companies.add(cells[2].textContent.trim());
                    types.add(cells[4].textContent.trim());
                 }
            });
            
             populateDropdown('countryFilter', Array.from(countries).sort());
             populateDropdown('companyFilter', Array.from(companies).sort());
             populateDropdown('typeFilter', Array.from(types).sort());
        }

        function populateDropdown(id, values) {
            const dropdown = document.getElementById(id);
            const firstOption = dropdown.options[0]; // Keep "All..." option
            dropdown.innerHTML = ''; // Clear existing options (except the first)
            dropdown.appendChild(firstOption); 
            
            values.forEach(value => {
                 if (value) { // Avoid adding empty options
                    const option = document.createElement('option');
                    option.value = value;
                    option.textContent = value;
                    dropdown.appendChild(option);
                 }
            });
        }
        // --- End Optional ---

    </script>
</body>
</html>