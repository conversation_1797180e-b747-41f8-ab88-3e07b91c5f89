"""
Tests for the authentication system
"""

import pytest
from fastapi.testclient import TestClient
from unittest.mock import patch

from app.main import app
from app.services.auth_service import AuthService
from app.core.config import get_settings


class TestAuthenticationService:
    """Test the AuthService class"""
    
    def test_validate_credentials_success(self):
        """Test successful credential validation"""
        settings = get_settings()
        result = AuthService.validate_credentials(
            settings.ADMIN_USERNAME, 
            settings.ADMIN_PASSWORD
        )
        assert result is True
    
    def test_validate_credentials_wrong_username(self):
        """Test credential validation with wrong username"""
        settings = get_settings()
        result = AuthService.validate_credentials(
            "wrong_user", 
            settings.ADMIN_PASSWORD
        )
        assert result is False
    
    def test_validate_credentials_wrong_password(self):
        """Test credential validation with wrong password"""
        settings = get_settings()
        result = AuthService.validate_credentials(
            settings.ADMIN_USERNAME, 
            "wrong_password"
        )
        assert result is False
    
    def test_validate_credentials_empty_values(self):
        """Test credential validation with empty values"""
        assert AuthService.validate_credentials("", "") is False
        assert AuthService.validate_credentials("user", "") is False
        assert AuthService.validate_credentials("", "pass") is False


class TestAuthenticationRoutes:
    """Test authentication routes"""
    
    @pytest.fixture
    def client(self):
        """Create test client"""
        return TestClient(app)
    
    def test_login_page_access(self, client):
        """Test accessing the login page"""
        response = client.get("/login")
        assert response.status_code == 200
        assert "Login" in response.text
        assert "username" in response.text
        assert "password" in response.text
    
    def test_login_page_redirect_when_authenticated(self, client):
        """Test login page redirects when already authenticated"""
        # First login
        settings = get_settings()
        login_data = {
            "username": settings.ADMIN_USERNAME,
            "password": settings.ADMIN_PASSWORD
        }
        
        # Login
        login_response = client.post("/auth/login", data=login_data)
        assert login_response.status_code == 200
        
        # Try to access login page again - should redirect
        response = client.get("/login")
        assert response.status_code == 200  # TestClient follows redirects
    
    def test_successful_login(self, client):
        """Test successful login"""
        settings = get_settings()
        login_data = {
            "username": settings.ADMIN_USERNAME,
            "password": settings.ADMIN_PASSWORD
        }
        
        response = client.post("/auth/login", data=login_data)
        assert response.status_code == 200
        assert "Login successful" in response.text
    
    def test_failed_login_wrong_credentials(self, client):
        """Test failed login with wrong credentials"""
        login_data = {
            "username": "wrong_user",
            "password": "wrong_password"
        }
        
        response = client.post("/auth/login", data=login_data)
        assert response.status_code == 401
        assert "Invalid username or password" in response.text
    
    def test_failed_login_empty_fields(self, client):
        """Test failed login with empty fields"""
        # Empty username
        response = client.post("/auth/login", data={"username": "", "password": "test"})
        assert response.status_code == 400
        assert "Please enter both username and password" in response.text
        
        # Empty password
        response = client.post("/auth/login", data={"username": "test", "password": ""})
        assert response.status_code == 400
        assert "Please enter both username and password" in response.text
    
    def test_logout(self, client):
        """Test logout functionality"""
        settings = get_settings()
        
        # First login
        login_data = {
            "username": settings.ADMIN_USERNAME,
            "password": settings.ADMIN_PASSWORD
        }
        client.post("/auth/login", data=login_data)
        
        # Then logout
        response = client.post("/auth/logout")
        assert response.status_code == 200  # TestClient follows redirects
    
    def test_manual_request_requires_authentication(self, client):
        """Test that manual-request route requires authentication"""
        response = client.get("/manual-request")
        assert response.status_code == 200  # TestClient follows redirects
        # Should be redirected to login page
        assert "Login" in response.text or response.url.path == "/login"
    
    def test_manual_request_access_when_authenticated(self, client):
        """Test accessing manual-request when authenticated"""
        settings = get_settings()
        
        # Login first
        login_data = {
            "username": settings.ADMIN_USERNAME,
            "password": settings.ADMIN_PASSWORD
        }
        client.post("/auth/login", data=login_data)
        
        # Access manual-request
        response = client.get("/manual-request")
        assert response.status_code == 200
        assert "Gas Bottle Price Scraper" in response.text
        assert "Run Scraper" in response.text
    
    def test_run_scraper_requires_authentication(self, client):
        """Test that run-scraper endpoint requires authentication"""
        response = client.post("/run-scraper", data={})
        assert response.status_code == 401
        assert "Authentication required" in response.text
    
    def test_run_scraper_access_when_authenticated(self, client):
        """Test accessing run-scraper when authenticated"""
        settings = get_settings()
        
        # Login first
        login_data = {
            "username": settings.ADMIN_USERNAME,
            "password": settings.ADMIN_PASSWORD
        }
        client.post("/auth/login", data=login_data)
        
        # Try to run scraper (should not get auth error)
        response = client.post("/run-scraper", data={})
        # Should not be 401 (auth error), might be other validation errors
        assert response.status_code != 401
    
    def test_cancel_scrapers_requires_authentication(self, client):
        """Test that cancel-all-scrapers endpoint requires authentication"""
        response = client.post("/cancel-all-scrapers")
        assert response.status_code == 401
        assert "Authentication required" in response.text
    
    def test_cancel_scrapers_access_when_authenticated(self, client):
        """Test accessing cancel-all-scrapers when authenticated"""
        settings = get_settings()
        
        # Login first
        login_data = {
            "username": settings.ADMIN_USERNAME,
            "password": settings.ADMIN_PASSWORD
        }
        client.post("/auth/login", data=login_data)
        
        # Try to cancel scrapers
        response = client.post("/cancel-all-scrapers")
        assert response.status_code == 200
        assert "canceled" in response.text


class TestSessionHandling:
    """Test session handling functionality"""
    
    @pytest.fixture
    def client(self):
        """Create test client"""
        return TestClient(app)
    
    def test_session_persistence(self, client):
        """Test that session persists across requests"""
        settings = get_settings()
        
        # Login
        login_data = {
            "username": settings.ADMIN_USERNAME,
            "password": settings.ADMIN_PASSWORD
        }
        client.post("/auth/login", data=login_data)
        
        # Make multiple requests - should stay authenticated
        response1 = client.get("/manual-request")
        response2 = client.get("/manual-request")
        
        assert response1.status_code == 200
        assert response2.status_code == 200
        assert "Run Scraper" in response1.text
        assert "Run Scraper" in response2.text
    
    def test_redirect_after_login(self, client):
        """Test redirect to original URL after login"""
        settings = get_settings()
        
        # Try to access protected page first
        client.get("/manual-request")
        
        # Then login
        login_data = {
            "username": settings.ADMIN_USERNAME,
            "password": settings.ADMIN_PASSWORD
        }
        response = client.post("/auth/login", data=login_data)
        
        # Should indicate redirect to manual-request
        assert response.status_code == 200
        assert "Login successful" in response.text


if __name__ == "__main__":
    pytest.main([__file__])
