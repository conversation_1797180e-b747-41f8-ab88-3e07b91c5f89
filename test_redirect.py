#!/usr/bin/env python3
"""
Test script to verify the unified authentication system
"""

import requests
import sys
from app.core.config import get_settings

def test_unified_auth_flow():
    """Test the unified authentication flow"""
    session = requests.Session()

    print("=== Testing Unified Authentication Flow ===")

    # 1. Try to access protected page
    print("1. Accessing /manual-request without auth...")
    response = session.get('http://127.0.0.1:8000/manual-request', allow_redirects=False)
    print(f"   Status: {response.status_code}")
    print(f"   Location: {response.headers.get('Location', 'None')}")

    # 2. Get login page
    print("\n2. Getting login page...")
    response = session.get('http://127.0.0.1:8000/login')
    print(f"   Status: {response.status_code}")
    print(f"   Contains login form: {'username' in response.text}")

    # 3. Submit login form
    print("\n3. Submitting login form...")
    settings = get_settings()
    login_data = {
        'username': settings.ADMIN_USERNAME,
        'password': settings.ADMIN_PASSWORD
    }
    response = session.post('http://127.0.0.1:8000/auth/login', data=login_data, allow_redirects=False)
    print(f"   Status: {response.status_code}")
    print(f"   Location: {response.headers.get('Location', 'None')}")
    print(f"   HX-Redirect: {response.headers.get('HX-Redirect', 'None')}")
    print(f"   Auth cookie set: {'auth_credentials' in response.cookies}")

    # 4. Try to access protected page again
    print("\n4. Accessing /manual-request after login...")
    response = session.get('http://127.0.0.1:8000/manual-request')
    print(f"   Status: {response.status_code}")
    print(f"   Contains scraper form: {'Run Scraper' in response.text}")

    # 5. Test admin route with same session (should work with unified auth)
    print("\n5. Testing admin route with unified auth...")
    response = session.get('http://127.0.0.1:8000/admin')
    print(f"   Status: {response.status_code}")
    print(f"   Contains admin interface: {'Configuration' in response.text}")

    # 6. Test logout
    print("\n6. Testing logout...")
    response = session.post('http://127.0.0.1:8000/auth/logout', allow_redirects=False)
    print(f"   Status: {response.status_code}")
    print(f"   Location: {response.headers.get('Location', 'None')}")

    # 7. Verify logout worked
    print("\n7. Verifying logout worked...")
    response = session.get('http://127.0.0.1:8000/manual-request', allow_redirects=False)
    print(f"   Status: {response.status_code}")
    print(f"   Redirected to login: {response.headers.get('Location') == '/login'}")

    print("\n=== Test completed ===")

def test_http_basic_auth_compatibility():
    """Test that existing HTTP Basic Auth still works for admin routes"""
    print("\n=== Testing HTTP Basic Auth Compatibility ===")

    settings = get_settings()
    auth = (settings.ADMIN_USERNAME, settings.ADMIN_PASSWORD)

    # Test admin route with HTTP Basic Auth
    print("1. Testing admin route with HTTP Basic Auth...")
    response = requests.get('http://127.0.0.1:8000/admin', auth=auth)
    print(f"   Status: {response.status_code}")
    print(f"   Contains admin interface: {'Configuration' in response.text}")

    print("\n=== HTTP Basic Auth Test completed ===")

if __name__ == "__main__":
    test_unified_auth_flow()
    test_http_basic_auth_compatibility()
