#!/usr/bin/env python3
"""
Test enhanced authentication UI across all pages.
"""

import requests
from urllib.parse import urljoin
from bs4 import BeautifulSoup

BASE_URL = "http://localhost:8000"
ADMIN_USERNAME = "admin"
ADMIN_PASSWORD = "adminrubis2025"

def test_auth_ui_all_pages():
    """Test authentication UI on all pages"""
    
    print("=== Testing Enhanced Authentication UI Across All Pages ===")
    
    session = requests.Session()
    
    # Test pages to check
    test_pages = [
        ("/database", "Database View", True),  # Public page
        ("/results", "Results Page", True),    # Public page
        ("/manual-request", "Manual Request", False),  # Protected page
        ("/admin", "Admin Panel", False),      # Protected page
        ("/admin/sites", "Site Configuration", False)  # Protected page
    ]

    # Test error page by trying to access a non-existent result file
    error_test_pages = [
        ("/results/nonexistent_file.csv", "Error Page (404)", True)  # Public error page
    ]
    
    print("\n1. Testing pages WITHOUT authentication...")
    all_test_pages = test_pages + error_test_pages
    for page_url, page_name, is_public in all_test_pages:
        print(f"\n   Testing {page_name} ({page_url})...")
        
        response = session.get(urljoin(BASE_URL, page_url), allow_redirects=False)
        
        if is_public:
            # For error pages, accept 404 status code
            expected_status = 404 if "Error Page" in page_name else 200
            if response.status_code == expected_status:
                if expected_status == 404:
                    print(f"     ✅ Error page returns expected 404 status")
                else:
                    print(f"     ✅ Public page accessible")

                # Check if auth header is present but shows "not authenticated"
                soup = BeautifulSoup(response.text, 'html.parser')
                auth_header = soup.find('div', class_='auth-header')

                if auth_header:
                    print(f"     ✅ Auth header present on public page")
                    if 'Not authenticated' in response.text or 'Login' in response.text:
                        print(f"     ✅ Shows 'not authenticated' status")
                    else:
                        print(f"     ❌ Does not show proper unauthenticated status")
                else:
                    print(f"     ❌ Auth header missing on public page")
            else:
                print(f"     ❌ Public page not accessible: {response.status_code} (expected {expected_status})")
        else:
            if response.status_code == 302:
                print(f"     ✅ Protected page redirects to login")
            else:
                print(f"     ❌ Protected page should redirect: {response.status_code}")
    
    print("\n2. Logging in...")
    login_response = session.post(
        urljoin(BASE_URL, "/auth/login"),
        data={"username": ADMIN_USERNAME, "password": ADMIN_PASSWORD}
    )
    
    if login_response.status_code == 200:
        print("     ✅ Login successful")
    else:
        print(f"     ❌ Login failed: {login_response.status_code}")
        return False
    
    print("\n3. Testing pages WITH authentication...")
    for page_url, page_name, is_public in all_test_pages:
        print(f"\n   Testing {page_name} ({page_url})...")
        
        response = session.get(urljoin(BASE_URL, page_url))

        # For error pages, accept 404 status code
        expected_status = 404 if "Error Page" in page_name else 200
        if response.status_code == expected_status:
            if expected_status == 404:
                print(f"     ✅ Error page returns expected 404 status when authenticated")
            else:
                print(f"     ✅ Page accessible when authenticated")
            
            # Check if auth header shows authenticated status
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Look for username display
            if ADMIN_USERNAME in response.text:
                print(f"     ✅ Username '{ADMIN_USERNAME}' displayed")
            else:
                print(f"     ❌ Username not displayed")
            
            # Look for authenticated status
            if 'Authenticated' in response.text:
                print(f"     ✅ Shows 'Authenticated' status")
            else:
                print(f"     ❌ Does not show authenticated status")
            
            # Look for logout button
            logout_forms = soup.find_all('form', action='/auth/logout')
            if logout_forms:
                print(f"     ✅ Logout button present ({len(logout_forms)} forms)")
                
                # Check for hx-boost="false"
                for form in logout_forms:
                    if form.get('hx-boost') == 'false':
                        print(f"     ✅ HTMX boost disabled on logout form")
                        break
                else:
                    print(f"     ❌ HTMX boost not disabled on logout forms")
            else:
                print(f"     ❌ Logout button missing")
        else:
            print(f"     ❌ Page not accessible when authenticated: {response.status_code} (expected {expected_status})")
    
    print("\n4. Testing logout functionality...")
    logout_response = session.post(urljoin(BASE_URL, "/auth/logout"), allow_redirects=False)
    
    if logout_response.status_code == 302:
        print("     ✅ Logout successful (302 redirect)")
        
        if logout_response.headers.get('HX-Redirect'):
            print("     ✅ HTMX redirect header present")
        else:
            print("     ❌ HTMX redirect header missing")
    else:
        print(f"     ❌ Logout failed: {logout_response.status_code}")
    
    print("\n5. Verifying logout worked...")
    test_protected = session.get(urljoin(BASE_URL, "/manual-request"), allow_redirects=False)
    
    if test_protected.status_code == 302:
        print("     ✅ Authentication cleared - redirected to login")
    else:
        print(f"     ❌ Authentication not cleared: {test_protected.status_code}")
    
    return True

if __name__ == "__main__":
    try:
        test_auth_ui_all_pages()
    except requests.exceptions.ConnectionError:
        print(f"❌ Could not connect to {BASE_URL}")
        print("Make sure the server is running")
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
