import asyncio
import base64
import glob
import io
import json
import logging
import os
import sys
from contextlib import asynccontextmanager
from datetime import datetime, timedelta
from io import BytesIO
from pathlib import Path
from secrets import compare_digest
from typing import Optional, Dict, List, Any

import pandas as pd
import xlsxwriter
import jsonschema
from jsonschema import validate, ValidationError
from PIL import Image
from fastapi import FastAPI, Request, BackgroundTasks, HTTPException, Depends, status
from fastapi.responses import HTMLR<PERSON>ponse, RedirectResponse, FileResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import HTTPBasic, HTTPBasicCredentials
from starlette.middleware.sessions import SessionMiddleware

from app.core.config import get_settings
from app.models.gas_price import GasPrice
from app.services.scraper_service import ScraperService
from app.services.scheduler_service import SchedulerService
from app.services.auth_service import AuthService, require_auth
from app.utils.exceptions import AuthenticationError
from app.db.database import init_db, get_gas_prices, save_gas_prices
from app.api.health import router as health_router
from app.middleware.performance import PerformanceMiddleware, RateLimitMiddleware
from app.services.monitoring_service import get_performance_monitor
from app.utils.graphs import generate_price_barplot, determine_bottle_type
from app.services.email_service import EmailService
from app.services.fallback_service import fallback_service

# Fix for Windows asyncio event loop policy
if sys.platform == "win32":
    asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Initialize settings
settings = get_settings()

# Initialize services
scraper_service = ScraperService()
scheduler_service = SchedulerService()

@asynccontextmanager
async def lifespan(_: FastAPI):
    # Startup code
    try:
        # run_migrations()
        init_db()
        # Start the scheduler with the scraper job callback
        scheduler_service.start_scheduler(run_scraper_task)
        logger.info("Application started successfully")
        yield
    finally:
        # Stop the scheduler on shutdown
        scheduler_service.stop_scheduler()
        logger.info("Application shutdown successfully")

# Create FastAPI app
app = FastAPI(
    title=settings.APP_NAME,
    description="""
    ## Gas Bottle Price Scraper API

    A comprehensive web scraping application for monitoring gas bottle prices across multiple countries and companies.

    ### Features
    - **Real-time Price Monitoring**: Automated scraping of gas bottle prices
    - **Multi-Country Support**: Portugal, Spain, France, Switzerland, and England
    - **Performance Monitoring**: Built-in metrics and health checks
    - **Data Export**: Excel reports with analytics and visualizations
    - **Admin Panel**: Configuration management and scheduling

    ### Authentication
    Admin endpoints require HTTP Basic Authentication with configured credentials.

    ### Rate Limiting
    API requests are limited to prevent abuse. Check response headers for current limits.

    ### Health Checks
    - `/health/` - Comprehensive health status
    - `/health/ready` - Kubernetes readiness probe
    - `/health/live` - Kubernetes liveness probe
    - `/health/metrics` - Performance metrics
    """,
    version=settings.VERSION,
    lifespan=lifespan,
    docs_url="/docs",
    redoc_url="/redoc",
    openapi_url="/openapi.json",
    contact={
        "name": "Gas Bottle Price Scraper",
        "url": "https://github.com/your-repo/gas-bottle-scraper",
    },
    license_info={
        "name": "MIT License",
        "url": "https://opensource.org/licenses/MIT",
    },
    tags_metadata=[
        {
            "name": "scraping",
            "description": "Gas bottle price scraping operations",
        },
        {
            "name": "database",
            "description": "Database operations and data retrieval",
        },
        {
            "name": "reports",
            "description": "Report generation and data export",
        },
        {
            "name": "admin",
            "description": "Administrative operations (requires authentication)",
        },
        {
            "name": "health",
            "description": "Health checks and monitoring",
        },
    ]
)

# Add session middleware (must be added before other middleware that might use sessions)
app.add_middleware(SessionMiddleware, secret_key=settings.SECRET_KEY)

# Add performance monitoring middleware
app.add_middleware(PerformanceMiddleware)

# Add rate limiting middleware
app.add_middleware(RateLimitMiddleware, requests_per_minute=settings.RATE_LIMIT_REQUESTS)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include health check router
app.include_router(health_router)

# Mount static files
app.mount("/static", StaticFiles(directory="static"), name="static")

# Setup templates
templates = Jinja2Templates(directory="templates")

# Setup security
security = HTTPBasic()

# Global variables for job management
jobs: Dict[str, Dict] = {}

def get_attr(obj, attr):
    return getattr(obj, attr, None) if hasattr(obj, attr) else obj.get(attr)

def save_results(results, name):
    """Save results to CSV and SQLite database"""
    try:
        # Save to CSV
        filename = f"static/results/results_{name}.csv"
        os.makedirs("static/results", exist_ok=True)
        
        # Convert the list of objects to a list of dictionaries
        results_dict = []
        for result in results:
            # Convert each GasPrice object to a dictionary by accessing its attributes
            gas_dict = {
                'id': result.id,
                'country': result.country,
                'company': result.company,
                'url': result.url,
                'element_nr': result.element,
                'product': result.product_type,
                'product_type': result.product_subtype,
                'comercial_name': result.name,
                'weight': result.weight,
                'price': result.price,
                'photo': result.image_path,
                'date_recorded': result.date_recorded,
                'uid': result.uid,
                'monthly_median_income': result.monthly_median_income,
                'yearly_median_income': result.yearly_median_income,
                'price_per_kg': result.price_per_kg,
                'price_as_percent_of_monthly_income': result.price_as_percent_of_monthly_income,
                'type': result.type,
                'is_fallback_data': result.is_fallback_data,
                'original_scrape_date': result.original_scrape_date
            }
            
            results_dict.append(gas_dict)

        # Create DataFrame from the list of dictionaries
        df = pd.DataFrame(results_dict)

        df.to_csv(filename, index=False)
        
        # Save to SQLite database
        save_gas_prices(results)
        print(f"Successfully saved {len(results)} records to database")
        
    except Exception as e:
        print(f"Error saving results: {str(e)}")
        raise

def save_test_results_to_csv(results: List[GasPrice], job_id: str) -> str:
    """Save test run results to CSV only (no database) with all fields"""
    try:
        # Create filename with timestamp and test prefix
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"static/data/test_run_{timestamp}.csv"

        # Ensure the directory exists
        os.makedirs(os.path.dirname(filename), exist_ok=True)

        # Convert results to list of dictionaries with all fields
        results_dict = []
        for result in results:
            gas_dict = {
                'id': result.id,
                'country': result.country,
                'company': result.company,
                'url': result.url,
                'element_nr': result.element,
                'product': result.product_type,
                'product_type': result.product_subtype,
                'comercial_name': result.name,
                'weight': result.weight,
                'price': result.price,
                'photo': result.image_path,
                'date_recorded': result.date_recorded,
                'uid': result.uid,
                'monthly_median_income': result.monthly_median_income,
                'yearly_median_income': result.yearly_median_income,
                'price_per_kg': result.price_per_kg,
                'price_as_percent_of_monthly_income': result.price_as_percent_of_monthly_income,
                'type': result.type,
                'is_fallback_data': result.is_fallback_data,
                'original_scrape_date': result.original_scrape_date
            }

            results_dict.append(gas_dict)

        # Create DataFrame from the data
        df = pd.DataFrame(results_dict)

        # Save to CSV
        df.to_csv(filename, index=False)

        logger.info(f"Test run results saved to {filename} with all fields and {len(results)} records")

        return filename

    except Exception as e:
        logger.error(f"Error saving test results: {str(e)}")
        raise

def filter_scraper_config(config: dict, selected_countries: List[str] = None, selected_companies: List[str] = None, selected_elements: List[str] = None) -> dict:
    """Filter scraper configuration based on target selection"""
    if not selected_countries and not selected_companies and not selected_elements:
        return config

    filtered_config = {}

    # Parse selected elements into a lookup structure
    element_lookup = {}
    if selected_elements:
        for element_key in selected_elements:
            parts = element_key.split('|')
            if len(parts) == 4:
                country, company, website, element = parts
                if country not in element_lookup:
                    element_lookup[country] = {}
                if company not in element_lookup[country]:
                    element_lookup[country][company] = {}
                if website not in element_lookup[country][company]:
                    element_lookup[country][company][website] = []
                element_lookup[country][company][website].append(element)

    # Filter the configuration
    for country_name, country_data in config.items():
        # Check if country should be included
        if selected_countries and country_name not in selected_countries:
            continue

        filtered_country = {}

        for company_name, company_data in country_data.items():
            # Check if company should be included
            if selected_companies and company_name not in selected_companies:
                continue

            filtered_company = {}

            for website_name, website_data in company_data.items():
                filtered_website = website_data.copy()

                # Filter elements if specific elements are selected
                if selected_elements and "Elements" in website_data:
                    if (country_name in element_lookup and
                        company_name in element_lookup[country_name] and
                        website_name in element_lookup[country_name][company_name]):

                        # Only include selected elements
                        selected_element_names = element_lookup[country_name][company_name][website_name]
                        filtered_elements = {
                            elem_name: elem_data
                            for elem_name, elem_data in website_data["Elements"].items()
                            if elem_name in selected_element_names
                        }
                        filtered_website["Elements"] = filtered_elements
                    else:
                        # No elements selected for this website, skip it
                        continue

                # Only include website if it has elements or no element filtering
                if "Elements" not in filtered_website or filtered_website["Elements"]:
                    filtered_company[website_name] = filtered_website

            if filtered_company:
                filtered_country[company_name] = filtered_company

        if filtered_country:
            filtered_config[country_name] = filtered_country

    logger.info(f"Filtered configuration: {len(filtered_config)} countries, targeting specific elements")
    return filtered_config

async def verify_admin(credentials: HTTPBasicCredentials = Depends(security)):
    """Verify admin credentials"""
    try:
        is_username_correct = compare_digest(
            credentials.username.encode("utf8"),
            settings.ADMIN_USERNAME.encode("utf8")
        )
        is_password_correct = compare_digest(
            credentials.password.encode("utf8"),
            settings.ADMIN_PASSWORD.encode("utf8")
        )
        
        if not (is_username_correct and is_password_correct):
            raise AuthenticationError("Invalid credentials")
        return credentials.username
    except Exception as e:
        logger.error(f"Authentication error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid credentials",
            headers={"WWW-Authenticate": "Basic"},
        )


@app.get("/", response_class=HTMLResponse)
async def index():
    """Redirect to the database page"""
    return RedirectResponse("/database", status_code=302)


# Authentication routes
@app.get("/login", response_class=HTMLResponse)
async def login_page(request: Request):
    """Display the login page"""
    # If already authenticated, redirect to manual-request
    if AuthService.is_authenticated(request):
        return RedirectResponse("/manual-request", status_code=302)

    return templates.TemplateResponse("login.html", {"request": request})


@app.post("/auth/login")
async def login(request: Request):
    """Handle login form submission"""
    try:
        form_data = await request.form()
        username = form_data.get("username", "").strip()
        password = form_data.get("password", "")

        if not username or not password:
            return HTMLResponse(
                content='<script>showError("Please enter both username and password");</script>',
                status_code=400
            )

        if AuthService.validate_credentials(username, password):
            AuthService.login_user(request, username)
            redirect_url = AuthService.get_redirect_after_login(request)

            # Return a script that redirects the page
            return HTMLResponse(
                content=f'<script>showSuccess("Login successful! Redirecting..."); setTimeout(() => window.location.href = "{redirect_url}", 1000);</script>',
                status_code=200
            )
        else:
            return HTMLResponse(
                content='<script>showError("Invalid username or password");</script>',
                status_code=401
            )

    except Exception as e:
        logger.error(f"Login error: {str(e)}")
        return HTMLResponse(
            content='<script>showError("An error occurred during login. Please try again.");</script>',
            status_code=500
        )


@app.post("/auth/logout")
async def logout(request: Request):
    """Handle logout"""
    AuthService.logout_user(request)
    return RedirectResponse("/login", status_code=302)


@app.get("/manual-request", response_class=HTMLResponse)
async def manual_request_page(request: Request):
    """Render the manual request page (protected)"""
    # Check authentication and redirect if not authenticated
    if not AuthService.is_authenticated(request):
        # Store the current URL for redirect after login
        request.session["redirect_after_login"] = "/manual-request"
        return RedirectResponse("/login", status_code=302)

    # Get current username for display
    username = AuthService.get_current_username(request)

    return templates.TemplateResponse("index.html", {
        "request": request,
        "authenticated": True,
        "username": username
    })

@app.get("/debug-target-selection", response_class=HTMLResponse)
async def debug_target_selection():
    """Debug page for target selection functionality"""
    with open("debug_frontend.html", "r", encoding="utf-8") as f:
        content = f.read()
    return HTMLResponse(content=content)


@app.post("/run-scraper", tags=["scraping"])
async def run_scraper(request: Request, background_tasks: BackgroundTasks):
    """Start a scraper job in the background with advanced options (protected)"""
    # Check authentication
    if not AuthService.is_authenticated(request):
        return HTMLResponse(
            content='<div class="mt-4 p-4 bg-red-100 text-red-800 rounded-md">Authentication required. Please <a href="/login" class="underline">login</a> to access this feature.</div>',
            status_code=401
        )

    job_id = datetime.now().strftime("%Y%m%d_%H%M%S")

    # Parse form data for advanced options
    form_data = await request.form()
    test_mode = form_data.get("test-mode") == "on"
    selected_countries = form_data.getlist("countries")
    selected_companies = form_data.getlist("companies")
    selected_elements = form_data.getlist("elements")

    # Store advanced options in job data
    jobs[job_id] = {
        "status": "running",
        "start_time": datetime.now().isoformat(),
        "result": None,
        "message": "Scraping in progress...",
        "test_mode": test_mode,
        "selected_countries": selected_countries,
        "selected_companies": selected_companies,
        "selected_elements": selected_elements
    }

    background_tasks.add_task(run_scraper_task, job_id, test_mode, selected_countries, selected_companies, selected_elements)

    # Save the last run time for the scheduler
    scheduler_service.save_last_run_time("scraper")

    # Update message based on mode
    mode_message = "Test run in progress (CSV only)..." if test_mode else "Scraping in progress..."

    response = templates.TemplateResponse(
        "job_status.html",
        {
            "request": request,
            "job_id": job_id,
            "status": "running",
            "message": mode_message,
            "test_mode": test_mode
        }
    )

    job_indicator_html = templates.TemplateResponse(
        "job_indicator.html",
        {
            "request": request,
            "job_id": job_id,
            "jobs": jobs  # Add this line to pass the jobs dictionary
        }
    ).body.decode()

    response.headers["HX-Trigger"] = json.dumps({
        "updateJobIndicator": {
            "html": job_indicator_html,
            "target": "#job-indicator-container"
        }
    })

    return response

async def run_scraper_task(job_id: Optional[str] = None, test_mode: bool = False, selected_countries: List[str] = None, selected_companies: List[str] = None, selected_elements: List[str] = None):
    if job_id is None:
        job_id = datetime.now().strftime("%Y%m%d_%H%M%S")

    try:
        # Load configuration dynamically
        scraper_config = settings.load_scraper_config()
        if not scraper_config:
            raise ValueError("No scraper configuration found")

        # Filter configuration based on target selection
        if selected_countries or selected_companies or selected_elements:
            scraper_config = filter_scraper_config(scraper_config, selected_countries, selected_companies, selected_elements)

        # Validate configuration
        if not settings.validate_scraper_config(scraper_config):
            raise ValueError("Invalid scraper configuration")

        results = await scraper_service.scrape(scraper_config)
        summary = scraper_service.get_summary_stats(results)

        # Handle results based on mode
        if test_mode:
            # Test mode: Save to CSV only, don't save to database
            csv_filename = save_test_results_to_csv(results, job_id)
            success_message = f"Test run completed! CSV file ready for download: {csv_filename}"
        else:
            # Normal mode: Save to database and CSV
            save_results(results, job_id)
            success_message = "Scraping completed successfully!"

        # Update job status after successful save
        jobs[job_id].update({
            "status": "completed",
            "end_time": datetime.now().isoformat(),
            "result": [price.model_dump() for price in results],
            "summary": summary,
            "message": success_message,
            "test_mode": test_mode,
            "csv_filename": csv_filename if test_mode else None
        })
        
        scheduler_service.save_last_run_time("scraper")
    except Exception as e:
        logger.error(f"Error in scraper task: {str(e)}")
        jobs[job_id].update({
            "status": "failed",
            "end_time": datetime.now().isoformat(),
            "result": None,
            "message": f"Error during scraping: {str(e)}"
        })

@app.get("/job-status/{job_id}")
async def job_status(request: Request, job_id: str):
    """Get the status of a job"""
    if job_id not in jobs:
        raise HTTPException(status_code=404, detail="Job not found")
    
    job_data = jobs[job_id]
    
    # Check if we need to update the status based on database save completion
    if job_data["status"] == "running" and "result" in job_data and job_data["result"]:
        job_data["status"] = "completed"
        job_data["message"] = "Scraping completed successfully!"

    return templates.TemplateResponse(
        "job_status.html",
        {
            "request": request,
            "job_id": job_id,
            "status": job_data["status"],
            "message": job_data["message"],
            "summary": job_data.get("summary", {}),
            "test_mode": job_data.get("test_mode", False),
            "csv_filename": job_data.get("csv_filename"),
        }
    )

@app.get("/database", response_class=HTMLResponse, tags=["database"])
async def database_view(
    request: Request,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    country: Optional[str] = None,  # Keep for backward compatibility
    company: Optional[str] = None,  # Keep for backward compatibility
    product_type: Optional[str] = None,  # Keep for backward compatibility
    countries: Optional[str] = None,  # New multi-select parameter
    companies: Optional[str] = None,  # New multi-select parameter
    product_types: Optional[str] = None  # New multi-select parameter
):
    """View database contents with optional filters"""
    try:
        # Default start_date to 30 days ago if not provided
        if not start_date:
            start_date = (datetime.now() - timedelta(days=30)).date().isoformat()

        if not end_date:
            end_date = datetime.now().date().isoformat()

        # Parse multi-select parameters
        country_list = countries.split(',') if countries else ([country] if country else None)
        company_list = companies.split(',') if companies else ([company] if company else None)
        product_type_list = product_types.split(',') if product_types else ([product_type] if product_type else None)

        prices = get_gas_prices(
            start_date=datetime.fromisoformat(start_date) if start_date else None,
            end_date=datetime.fromisoformat(end_date).replace(hour=23, minute=59, second=59, microsecond=0) if end_date else None,
            countries=country_list,
            companies=company_list,
            product_types=product_type_list
        )

        # Get all available options for filters (not filtered by current selection)
        all_prices = get_gas_prices(
            start_date=datetime.fromisoformat(start_date) if start_date else None,
            end_date=datetime.fromisoformat(end_date).replace(hour=23, minute=59, second=59, microsecond=0) if end_date else None
        )

        all_countries = sorted({get_attr(p, 'country') for p in all_prices if get_attr(p, 'country')})
        all_companies = sorted({get_attr(p, 'company') for p in all_prices if get_attr(p, 'company')})
        all_product_types = sorted({get_attr(p, 'product_type') for p in all_prices if get_attr(p, 'product_type')})

        return templates.TemplateResponse(
            "database_view.html",
            {
                "request": request,
                "prices": prices,
                "companies": all_companies,
                "product_types": all_product_types,
                "countries": all_countries,
                "datetime": datetime,  # Pass datetime to the template
                "timedelta": timedelta  # Pass timedelta to the template
            }
        )
    except Exception as e:
        logger.error(f"Error retrieving database view: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "version": "1.0.0",
        "timestamp": datetime.now().isoformat()
    }


@app.get("/fallback-statistics", tags=["admin"])
async def get_fallback_statistics(admin: str = Depends(verify_admin)):
    """Get fallback mechanism statistics (admin only)"""
    try:
        stats = fallback_service.get_fallback_statistics()

        # Add additional database statistics
        from app.db.database import get_db_connection
        with get_db_connection() as conn:
            cursor = conn.cursor()

            # Count total fallback records in database
            cursor.execute("SELECT COUNT(*) FROM gas_prices WHERE is_fallback_data = TRUE")
            total_fallback_records = cursor.fetchone()[0]

            # Count total records
            cursor.execute("SELECT COUNT(*) FROM gas_prices")
            total_records = cursor.fetchone()[0]

            # Get fallback percentage
            fallback_percentage = (total_fallback_records / total_records * 100) if total_records > 0 else 0

            # Get recent fallback activity (last 7 days)
            cursor.execute("""
                SELECT COUNT(*) FROM gas_prices
                WHERE is_fallback_data = TRUE
                AND date_recorded >= datetime('now', '-7 days')
            """)
            recent_fallback_records = cursor.fetchone()[0]

        stats.update({
            "database_statistics": {
                "total_fallback_records": total_fallback_records,
                "total_records": total_records,
                "fallback_percentage": round(fallback_percentage, 2),
                "recent_fallback_records_7_days": recent_fallback_records
            }
        })

        return stats

    except Exception as e:
        logger.error(f"Error retrieving fallback statistics: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving fallback statistics: {str(e)}"
        )


@app.post("/reset-fallback-statistics", tags=["admin"])
async def reset_fallback_statistics(admin: str = Depends(verify_admin)):
    """Reset fallback mechanism statistics (admin only)"""
    try:
        fallback_service.reset_statistics()
        return {
            "status": "success",
            "message": "Fallback statistics have been reset",
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"Error resetting fallback statistics: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error resetting fallback statistics: {str(e)}"
        )

@app.get("/results", response_class=HTMLResponse)
async def results_page(request: Request, page: int = 1, per_page: int = 10):
    """View all available results"""
    results_dir = settings.RESULTS_DIR
    if not os.path.exists(results_dir):
        os.makedirs(results_dir)
    files = []
    for file in os.listdir(results_dir):
        if file.endswith(".csv"):
            file_path = os.path.join(results_dir, file)
            timestamp = os.path.getmtime(file_path)
            date = datetime.fromtimestamp(timestamp).strftime("%Y-%m-%d %H:%M:%S")
            files.append({"name": file, "date": date})
    files.sort(key=lambda x: x["name"], reverse=True)
    
    # Calculate pagination
    total_records = len(files)
    total_pages = (total_records + per_page - 1) // per_page
    page = max(1, min(page, total_pages))  # Ensure page is within valid range
    
    # Get records for current page
    start_idx = (page - 1) * per_page
    end_idx = start_idx + per_page
    paginated_files = files[start_idx:end_idx]
    
    return templates.TemplateResponse(
        "results.html", 
        {
            "request": request, 
            "files": paginated_files,
            "pagination": {
                "current_page": page,
                "total_pages": total_pages,
                "total_records": total_records,
                "per_page": per_page
            },
            "min": min,  # Pass the min function to the template
            "max": max   # Also pass max for consistency
        }
    )

@app.get("/results/{filename}")
async def view_result(request: Request, filename: str):
    file_path = f"{settings.RESULTS_DIR}/{filename}"
    if not os.path.exists(file_path):
        return templates.TemplateResponse("error.html", {"request": request, "message": "Result file not found"}, status_code=404)
    df = pd.read_csv(file_path)
    summary = {
        "total_items": len(df),
        "countries": df["country"].nunique(),
        "companies": df["company"].nunique(),
        "price_range": f"{df['price'].min():.2f}€ - {df['price'].max():.2f}€",
        "avg_price": f"{df['price'].mean():.2f}€"
    }
    countries = df["country"].unique().tolist()
    companies = df["company"].unique().tolist()
    product_types = df["product_type"].unique().tolist()
    records = df.to_dict(orient="records")
    return templates.TemplateResponse("view_result.html", {
        "request": request,
        "filename": filename,
        "summary": summary,
        "records": records,
        "countries": countries,
        "companies": companies,
        "product_types": product_types
    })

@app.post("/search-results/{filename}")
async def search_results(request: Request, filename: str):
    form_data = await request.form()
    search_term = form_data.get("search", "").lower()
    file_path = f"{settings.RESULTS_DIR}/{filename}"
    if not os.path.exists(file_path):
        return HTMLResponse("<p>File not found</p>")
    df = pd.read_csv(file_path)
    if search_term:
        filtered_df = df[
            df['country'].str.lower().str.contains(search_term, na=False) |
            df['company'].str.lower().str.contains(search_term, na=False) |
            df['product_type'].str.lower().str.contains(search_term, na=False) |
            df['name'].str.lower().str.contains(search_term, na=False)
        ]
    else:
        filtered_df = df
    records = filtered_df.to_dict(orient="records")
    return templates.TemplateResponse("results_table.html", {"request": request, "records": records})

@app.delete("/results/{filename}")
async def delete_result(request: Request, filename: str):
    file_path = Path(settings.RESULTS_DIR) / filename
    if file_path.exists():
        os.remove(file_path)
        # Return updated file list
        files = []
        for file in os.listdir(settings.RESULTS_DIR):
            if file.endswith(".csv"):
                file_path = os.path.join(settings.RESULTS_DIR, file)
                timestamp = os.path.getmtime(file_path)
                date = datetime.fromtimestamp(timestamp).strftime("%Y-%m-%d %H:%M:%S")
                files.append({"name": file, "date": date})
        files.sort(key=lambda x: x["name"], reverse=True)
        return templates.TemplateResponse("results_list.html", {"request": request, "files": files})
    else:
        raise HTTPException(status_code=404, detail="File not found")

@app.post("/cancel-all-scrapers")
async def cancel_all_scrapers(request: Request):
    """Cancel all running scraper jobs (protected)"""
    # Check authentication
    if not AuthService.is_authenticated(request):
        return HTMLResponse(
            content='<div class="mt-4 p-4 bg-red-100 text-red-800 rounded-md">Authentication required to cancel scrapers.</div>',
            status_code=401
        )

    # This should interact with the scraper_service to cancel jobs if implemented
    # For now, just return a message
    return HTMLResponse("<div class='mt-4 p-4 bg-green-100 text-green-800 rounded-md'>All running scraper jobs have been canceled.</div>")

@app.get("/download-test-csv/{filename}")
async def download_test_csv(filename: str):
    """Download test run CSV file"""
    try:
        file_path = f"static/data/{filename}"
        if not os.path.exists(file_path):
            raise HTTPException(status_code=404, detail="File not found")

        return FileResponse(
            path=file_path,
            filename=filename,
            media_type='text/csv'
        )
    except Exception as e:
        logger.error(f"Error downloading test CSV: {str(e)}")
        raise HTTPException(status_code=500, detail="Error downloading file")

@app.post("/api/get-companies")
async def get_companies(request: Request):
    """Get available companies for selected countries"""
    try:
        data = await request.json()
        selected_countries = data.get('countries', [])

        if not selected_countries:
            return {"companies": []}

        companies = set()

        # Load site configuration files for selected countries
        for country in selected_countries:
            config_file = f"config/{country.lower()}_sites.json"
            if os.path.exists(config_file):
                try:
                    with open(config_file, 'r', encoding='utf-8') as f:
                        config_data = json.load(f)

                    # Extract companies from the configuration
                    for country_data in config_data.values():
                        companies.update(country_data.keys())

                except Exception as e:
                    logger.warning(f"Error reading {config_file}: {e}")

        return {"companies": sorted(list(companies))}

    except Exception as e:
        logger.error(f"Error getting companies: {str(e)}")
        raise HTTPException(status_code=500, detail="Error loading companies")

@app.post("/api/get-elements")
async def get_elements(request: Request):
    """Get available elements for selected countries and companies"""
    try:
        data = await request.json()
        selected_countries = data.get('countries', [])
        selected_companies = data.get('companies', [])

        if not selected_countries or not selected_companies:
            return {"elements": []}

        elements = []

        # Load site configuration files for selected countries
        for country in selected_countries:
            config_file = f"config/{country.lower()}_sites.json"
            if os.path.exists(config_file):
                try:
                    with open(config_file, 'r', encoding='utf-8') as f:
                        config_data = json.load(f)

                    # Extract elements from selected companies
                    for country_name, country_data in config_data.items():
                        for company_name, company_data in country_data.items():
                            if company_name in selected_companies:
                                for website_name, website_data in company_data.items():
                                    if "Elements" in website_data:
                                        for element_name, element_data in website_data["Elements"].items():
                                            elements.append({
                                                "key": f"{country}|{company_name}|{website_name}|{element_name}",
                                                "name": element_name,
                                                "country": country,
                                                "company": company_name,
                                                "website": website_name,
                                                "uid": element_data.get("uid", ""),
                                                "product": element_data.get("product", "")
                                            })

                except Exception as e:
                    logger.warning(f"Error reading {config_file}: {e}")

        return {"elements": elements}

    except Exception as e:
        logger.error(f"Error getting elements: {str(e)}")
        raise HTTPException(status_code=500, detail="Error loading elements")

@app.get("/database-table")
async def database_table(
    request: Request,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    countries: Optional[str] = None,  # Changed to support multiple values
    companies: Optional[str] = None,  # Changed to support multiple values
    product_types: Optional[str] = None  # Changed to support multiple values
):
    # Log the incoming parameters for debugging
    logger.info(f"database_table endpoint called with: start_date={start_date}, end_date={end_date}, "
                f"countries={countries}, companies={companies}, product_types={product_types}")

    # Parse comma-separated values for multi-select
    # Handle empty strings and whitespace-only strings
    country_list = None
    if countries and countries.strip():
        country_list = [c.strip() for c in countries.split(',') if c.strip()]
        if not country_list:  # If all values were empty after stripping
            country_list = None

    company_list = None
    if companies and companies.strip():
        company_list = [c.strip() for c in companies.split(',') if c.strip()]
        if not company_list:  # If all values were empty after stripping
            company_list = None

    product_type_list = None
    if product_types and product_types.strip():
        product_type_list = [p.strip() for p in product_types.split(',') if p.strip()]
        if not product_type_list:  # If all values were empty after stripping
            product_type_list = None

    logger.info(f"Parsed filter lists: countries={country_list}, companies={company_list}, product_types={product_type_list}")

    prices = get_gas_prices(
        start_date=datetime.fromisoformat(start_date) if start_date else None,
        end_date=datetime.fromisoformat(end_date).replace(hour=23, minute=59, second=59, microsecond=0) if end_date else None,
        countries=country_list,  # Updated to use list
        companies=company_list,  # Updated to use list
        product_types=product_type_list  # Updated to use list
    )

    logger.info(f"Returning {len(prices)} records to template")

    return templates.TemplateResponse(
        "database_table.html",
        {
            "request": request,
            "records": prices
        }
    )


@app.get("/api/filter-options", tags=["database"])
async def get_filter_options(
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    countries: Optional[str] = None,
    companies: Optional[str] = None,
    product_types: Optional[str] = None
):
    """Get available filter options based on current selections"""
    # Parse comma-separated values with proper empty string handling
    country_list = None
    if countries and countries.strip():
        country_list = [c.strip() for c in countries.split(',') if c.strip()]
        if not country_list:
            country_list = None

    company_list = None
    if companies and companies.strip():
        company_list = [c.strip() for c in companies.split(',') if c.strip()]
        if not company_list:
            company_list = None

    product_type_list = None
    if product_types and product_types.strip():
        product_type_list = [p.strip() for p in product_types.split(',') if p.strip()]
        if not product_type_list:
            product_type_list = None

    # Get all data with current filters
    prices = get_gas_prices(
        start_date=datetime.fromisoformat(start_date) if start_date else None,
        end_date=datetime.fromisoformat(end_date).replace(hour=23, minute=59, second=59, microsecond=0) if end_date else None,
        countries=country_list,
        companies=company_list,
        product_types=product_type_list
    )

    # Extract unique values for each filter
    available_countries = sorted({get_attr(p, 'country') for p in prices if get_attr(p, 'country')})
    available_companies = sorted({get_attr(p, 'company') for p in prices if get_attr(p, 'company')})
    available_product_types = sorted({get_attr(p, 'product_type') for p in prices if get_attr(p, 'product_type')})

    return {
        "countries": available_countries,
        "companies": available_companies,
        "product_types": available_product_types,
        "total_records": len(prices)
    }


@app.get("/api/filter-companies", tags=["database"])
async def get_filter_companies(
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    countries: Optional[str] = None
):
    """Get available companies based on selected countries"""
    # Parse comma-separated values with proper empty string handling
    country_list = None
    if countries and countries.strip():
        country_list = [c.strip() for c in countries.split(',') if c.strip()]
        if not country_list:
            country_list = None

    prices = get_gas_prices(
        start_date=datetime.fromisoformat(start_date) if start_date else None,
        end_date=datetime.fromisoformat(end_date).replace(hour=23, minute=59, second=59, microsecond=0) if end_date else None,
        countries=country_list
    )

    companies = sorted({get_attr(p, 'company') for p in prices if get_attr(p, 'company')})

    return {
        "companies": companies,
        "count": len(companies)
    }


@app.get("/api/filter-product-types", tags=["database"])
async def get_filter_product_types(
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    countries: Optional[str] = None,
    companies: Optional[str] = None
):
    """Get available product types based on selected countries and companies"""
    # Parse comma-separated values with proper empty string handling
    country_list = None
    if countries and countries.strip():
        country_list = [c.strip() for c in countries.split(',') if c.strip()]
        if not country_list:
            country_list = None

    company_list = None
    if companies and companies.strip():
        company_list = [c.strip() for c in companies.split(',') if c.strip()]
        if not company_list:
            company_list = None

    prices = get_gas_prices(
        start_date=datetime.fromisoformat(start_date) if start_date else None,
        end_date=datetime.fromisoformat(end_date).replace(hour=23, minute=59, second=59, microsecond=0) if end_date else None,
        countries=country_list,
        companies=company_list
    )

    product_types = sorted({get_attr(p, 'product_type') for p in prices if get_attr(p, 'product_type')})

    return {
        "product_types": product_types,
        "count": len(product_types)
    }


@app.get("/htmx/filter-companies", tags=["database"])
async def get_filter_companies_htmx(
    request: Request,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    countries: Optional[str] = None
):
    """Get available companies as HTML template for HTMX updates"""
    # Parse comma-separated values with proper empty string handling
    country_list = None
    if countries and countries.strip():
        country_list = [c.strip() for c in countries.split(',') if c.strip()]
        if not country_list:
            country_list = None

    prices = get_gas_prices(
        start_date=datetime.fromisoformat(start_date) if start_date else None,
        end_date=datetime.fromisoformat(end_date).replace(hour=23, minute=59, second=59, microsecond=0) if end_date else None,
        countries=country_list
    )

    companies = sorted({get_attr(p, 'company') for p in prices if get_attr(p, 'company')})

    return templates.TemplateResponse(
        "filter_companies.html",
        {
            "request": request,
            "companies": companies
        }
    )


@app.get("/htmx/filter-product-types", tags=["database"])
async def get_filter_product_types_htmx(
    request: Request,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    countries: Optional[str] = None,
    companies: Optional[str] = None
):
    """Get available product types as HTML template for HTMX updates"""
    # Parse comma-separated values with proper empty string handling
    country_list = None
    if countries and countries.strip():
        country_list = [c.strip() for c in countries.split(',') if c.strip()]
        if not country_list:
            country_list = None

    company_list = None
    if companies and companies.strip():
        company_list = [c.strip() for c in companies.split(',') if c.strip()]
        if not company_list:
            company_list = None

    prices = get_gas_prices(
        start_date=datetime.fromisoformat(start_date) if start_date else None,
        end_date=datetime.fromisoformat(end_date) if end_date else None, #.replace(hour=23, minute=59, second=59, microsecond=0)
        countries=country_list,
        companies=company_list
    )

    product_types = sorted({get_attr(p, 'product_type') for p in prices if get_attr(p, 'product_type')})

    return templates.TemplateResponse(
        "filter_product_types.html",
        {
            "request": request,
            "product_types": product_types
        }
    )


@app.get("/admin", response_class=HTMLResponse)
async def admin_page(request: Request, _: str = Depends(verify_admin)):
    try:
        with open("config.json", "r") as f:
            config_json = f.read()
            config_dict = json.loads(config_json)
            config_json = json.dumps(config_dict, indent=4)
        return templates.TemplateResponse("admin.html", {"request": request, "config_json": config_json})
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/admin/save")
async def save_config(request: Request, _: str = Depends(verify_admin)):
    """Save configuration and reload scheduler"""
    try:
        form_data = await request.form()
        config_data = json.loads(form_data.get("config", "{}"))
        
        # Save the new configuration
        with open("config.json", "w") as f:
            json.dump(config_data, f, indent=2)
        
        # Reload the scheduler with new configuration
        scheduler_service.setup_schedule(run_scraper_task)
        
        # Return only the status message partial
        return templates.TemplateResponse(
            "admin_status.html",
            {
                "request": request,
                "message": "Configuration saved and scheduler reloaded successfully",
                "success": True
            }
        )
    except Exception as e:
        logger.error(f"Error saving configuration: {str(e)}")
        return templates.TemplateResponse(
            "admin_status.html",
            {
                "request": request,
                "message": f"Error saving configuration: {str(e)}",
                "success": False
            }
        )

@app.get("/admin/reload")
async def reload_config(request: Request, _: str = Depends(verify_admin)):
    """Reload the scheduler configuration"""
    try:
        scheduler_service.setup_schedule(run_scraper_task)
        return templates.TemplateResponse(
            "admin.html",
            {
                "request": request,
                "message": "Scheduler reloaded successfully"
            }
        )
    except Exception as e:
        logger.error(f"Error reloading scheduler: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error reloading scheduler: {str(e)}"
        )

# Site Configuration Management Endpoints

def load_json_schema():
    """Load the JSON schema for site configuration validation"""
    try:
        with open("config/site_config_schema.json", "r") as f:
            return json.load(f)
    except Exception as e:
        logger.error(f"Error loading JSON schema: {e}")
        return None

def get_site_files():
    """Get list of all *_sites.json files"""
    return glob.glob("config/*_sites.json")

def validate_site_config(config_data):
    """Validate site configuration against JSON schema"""
    schema = load_json_schema()
    if not schema:
        return False, "Could not load validation schema"

    try:
        validate(instance=config_data, schema=schema)
        return True, "Valid configuration"
    except ValidationError as e:
        return False, f"Validation error: {e.message}"
    except Exception as e:
        return False, f"Validation error: {str(e)}"

@app.get("/admin/sites", response_class=HTMLResponse, tags=["admin"])
async def admin_sites_page(request: Request, _: str = Depends(verify_admin)):
    """Site configuration management page"""
    try:
        site_files = get_site_files()
        countries = []

        for file_path in site_files:
            country_name = Path(file_path).stem.replace('_sites', '').title()
            countries.append({
                'name': country_name,
                'file': Path(file_path).name,
                'path': file_path
            })

        return templates.TemplateResponse(
            "admin_sites.html",
            {
                "request": request,
                "countries": countries
            }
        )
    except Exception as e:
        logger.error(f"Error loading sites admin page: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/admin/sites/{country_file}", tags=["admin"])
async def get_site_config(country_file: str, _: str = Depends(verify_admin)):
    """Get site configuration for a specific country"""
    try:
        file_path = f"config/{country_file}"
        if not os.path.exists(file_path):
            raise HTTPException(status_code=404, detail="Configuration file not found")

        with open(file_path, "r", encoding="utf-8") as f:
            config_data = json.load(f)

        return {
            "success": True,
            "data": config_data,
            "file": country_file
        }
    except Exception as e:
        logger.error(f"Error loading site config {country_file}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/admin/sites/{country_file}", tags=["admin"])
async def save_site_config(
    country_file: str,
    request: Request,
    _: str = Depends(verify_admin)
):
    """Save site configuration for a specific country"""
    try:
        form_data = await request.form()
        config_json = form_data.get("config_data")

        if not config_json:
            raise HTTPException(status_code=400, detail="No configuration data provided")

        # Parse JSON
        try:
            config_data = json.loads(config_json)
        except json.JSONDecodeError as e:
            raise HTTPException(status_code=400, detail=f"Invalid JSON: {str(e)}")

        # Validate configuration
        is_valid, validation_message = validate_site_config(config_data)
        if not is_valid:
            raise HTTPException(status_code=400, detail=validation_message)

        # Create backup
        file_path = f"config/{country_file}"
        backup_path = f"config/backup_{country_file}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        if os.path.exists(file_path):
            import shutil
            shutil.copy2(file_path, backup_path)

        # Save new configuration
        with open(file_path, "w", encoding="utf-8") as f:
            json.dump(config_data, f, indent=2, ensure_ascii=False)

        return {
            "success": True,
            "message": f"Configuration saved successfully for {country_file}",
            "backup_created": backup_path if os.path.exists(file_path) else None
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error saving site config {country_file}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/admin/sites/{country_file}/add-element", tags=["admin"])
async def add_site_element(
    country_file: str,
    request: Request,
    _: str = Depends(verify_admin)
):
    """Add a new element to a website configuration"""
    try:
        form_data = await request.form()
        country = form_data.get("country")
        company = form_data.get("company")
        website = form_data.get("website")
        element_name = form_data.get("element_name")
        element_data = json.loads(form_data.get("element_data", "{}"))

        if not all([country, company, website, element_name]):
            raise HTTPException(status_code=400, detail="Missing required fields")

        # Load current configuration
        file_path = f"config/{country_file}"
        with open(file_path, "r", encoding="utf-8") as f:
            config_data = json.load(f)

        # Navigate to the correct location and add element
        if country not in config_data:
            config_data[country] = {}
        if company not in config_data[country]:
            config_data[country][company] = {}
        if website not in config_data[country][company]:
            config_data[country][company][website] = {"url": "", "Elements": {}}
        if "Elements" not in config_data[country][company][website]:
            config_data[country][company][website]["Elements"] = {}

        config_data[country][company][website]["Elements"][element_name] = element_data

        # Validate and save
        is_valid, validation_message = validate_site_config(config_data)
        if not is_valid:
            raise HTTPException(status_code=400, detail=validation_message)

        with open(file_path, "w", encoding="utf-8") as f:
            json.dump(config_data, f, indent=2, ensure_ascii=False)

        return {"success": True, "message": "Element added successfully"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error adding element to {country_file}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.delete("/admin/sites/{country_file}/delete-element", tags=["admin"])
async def delete_site_element(
    country_file: str,
    request: Request,
    _: str = Depends(verify_admin)
):
    """Delete an element from a website configuration"""
    try:
        form_data = await request.form()
        country = form_data.get("country")
        company = form_data.get("company")
        website = form_data.get("website")
        element_name = form_data.get("element_name")

        if not all([country, company, website, element_name]):
            raise HTTPException(status_code=400, detail="Missing required fields")

        # Load current configuration
        file_path = f"config/{country_file}"
        with open(file_path, "r", encoding="utf-8") as f:
            config_data = json.load(f)

        # Navigate and delete element
        try:
            del config_data[country][company][website]["Elements"][element_name]
        except KeyError:
            raise HTTPException(status_code=404, detail="Element not found")

        # Save configuration
        with open(file_path, "w", encoding="utf-8") as f:
            json.dump(config_data, f, indent=2, ensure_ascii=False)

        return {"success": True, "message": "Element deleted successfully"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting element from {country_file}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/admin/sites/schema", tags=["admin"])
async def get_site_schema(_: str = Depends(verify_admin)):
    """Get the JSON schema for site configuration"""
    try:
        schema = load_json_schema()
        if not schema:
            raise HTTPException(status_code=500, detail="Could not load schema")
        return {"success": True, "schema": schema}
    except Exception as e:
        logger.error(f"Error loading schema: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/job-indicator")
async def job_indicator(request: Request):
    """Get the job indicator partial template"""
    running_job = None
    for job_id, job_data in jobs.items():
        if job_data["status"] == "running":
            running_job = job_id
            break
    return templates.TemplateResponse(
        "job_indicator.html",
        {
            "request": request,
            "job_id": running_job,
            "jobs": jobs  # Add this line to pass the jobs dictionary
        }
    )

@app.get("/generate-report")
async def generate_report(
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    # Legacy single-select parameters (for backward compatibility)
    country: Optional[str] = None,
    company: Optional[str] = None,
    product_type: Optional[str] = None,
    # New multi-select parameters
    countries: Optional[str] = None,
    companies: Optional[str] = None,
    product_types: Optional[str] = None
):
    """Generate an Excel report based on filtered data, including price change analytics"""
    try:
        # Log the incoming parameters for debugging
        logger.info(f"generate_report endpoint called with: start_date={start_date}, end_date={end_date}, "
                    f"countries={countries}, companies={companies}, product_types={product_types}")

        # Parse multi-select parameters with proper empty string handling
        # Handle empty strings and whitespace-only strings
        country_list = None
        if countries and countries.strip():
            country_list = [c.strip() for c in countries.split(',') if c.strip()]
            if not country_list:  # If all values were empty after stripping
                country_list = None
        elif country and country.strip():  # Fallback to legacy single parameter
            country_list = [country.strip()]

        company_list = None
        if companies and companies.strip():
            company_list = [c.strip() for c in companies.split(',') if c.strip()]
            if not company_list:  # If all values were empty after stripping
                company_list = None
        elif company and company.strip():  # Fallback to legacy single parameter
            company_list = [company.strip()]

        product_type_list = None
        if product_types and product_types.strip():
            product_type_list = [p.strip() for p in product_types.split(',') if p.strip()]
            if not product_type_list:  # If all values were empty after stripping
                product_type_list = None
        elif product_type and product_type.strip():  # Fallback to legacy single parameter
            product_type_list = [product_type.strip()]

        logger.info(f"Parsed filter lists for report: countries={country_list}, companies={company_list}, product_types={product_type_list}")

        # Get filtered data using multi-select parameters
        # Apply the same date handling logic as /database-table endpoint
        prices = get_gas_prices(
            start_date=datetime.fromisoformat(start_date) if start_date else None,
            end_date=datetime.fromisoformat(end_date).replace(hour=23, minute=59, second=59, microsecond=0) if end_date else None,
            countries=country_list,
            companies=company_list,
            product_types=product_type_list
        )

        logger.info(f"Report generation query returned {len(prices)} records")

        print(prices)

         # Create an in-memory Excel file
        output = BytesIO()
        workbook = xlsxwriter.Workbook(output)
        
        # Create a worksheet for raw data
        raw_data_sheet = workbook.add_worksheet("Raw Data")
        
        # Create a worksheet for analytics
        analytics_sheet = workbook.add_worksheet("Price Change Analytics")
        
        # Add headers to raw data sheet
        headers = [
            'UID', 'Date Recorded', 'Country', 'Company', 'Product Type',
            'Product Subtype', 'Type', 'Name', 'Weight', 'Price',
            'Price per KG', 'Price as % of Monthly Income', 'URL', 'Photo URL',
            'Is Fallback Data', 'Original Scrape Date'
        ]

        for col, header in enumerate(headers):
            raw_data_sheet.write(0, col, header)

        # Add data to raw data sheet
        for row, price in enumerate(prices, 1):
            raw_data_sheet.write(row, 0, price.uid if hasattr(price, 'uid') and price.uid else "")
            raw_data_sheet.write(row, 1, price.date_recorded.isoformat())
            raw_data_sheet.write(row, 2, price.country)
            raw_data_sheet.write(row, 3, price.company)
            raw_data_sheet.write(row, 4, price.product_type)
            raw_data_sheet.write(row, 5, price.product_subtype)
            raw_data_sheet.write(row, 6, price.type)
            raw_data_sheet.write(row, 7, price.name)
            raw_data_sheet.write(row, 8, price.weight)
            raw_data_sheet.write(row, 9, price.price)
            raw_data_sheet.write(row, 10, price.price_per_kg if price.price_per_kg else "")
            raw_data_sheet.write(row, 11, price.price_as_percent_of_monthly_income if price.price_as_percent_of_monthly_income else "")
            raw_data_sheet.write(row, 12, price.url)
            raw_data_sheet.write(row, 13, price.image_path if hasattr(price, 'image_path') and price.image_path else "")
            raw_data_sheet.write(row, 14, "Yes" if price.is_fallback_data else "No")
            raw_data_sheet.write(row, 15, price.original_scrape_date.isoformat() if price.original_scrape_date else "")

        # Calculate price changes for each unique product
        # Hybrid approach: Group by UID if available, otherwise use product attributes
        product_data = {}
        
        for price in prices:
            # Check if price has a valid UID
            has_valid_uid = hasattr(price, 'uid') and price.uid
            
            if has_valid_uid:
                # Use UID as the key for products with UIDs
                key = price.uid
            else:
                # Fallback to composite key for products without UIDs
                key = (
                    price.country, 
                    price.company, 
                    price.product_type, 
                    price.product_subtype, 
                    price.type, 
                    price.name, 
                    price.weight
                )
            
            if key not in product_data:
                # Get photo URL (if available) - Fix the photo URL extraction
                photo_url = price.image_path if hasattr(price, 'image_path') and price.image_path else None
                
                # Debug logging to check photo URLs
                if photo_url:
                    logger.info(f"Found photo URL: {photo_url}")
                
                product_data[key] = {
                    'points': [],
                    'metadata': {
                        'uid': price.uid if has_valid_uid else None,
                        'country': price.country,
                        'company': price.company,
                        'product_type': price.product_type,
                        'product_subtype': price.product_subtype,
                        'type': price.type,
                        'name': price.name,
                        'weight': price.weight,
                        'photo_url': photo_url  # This should now be populated
                    }
                }
                
            product_data[key]['points'].append({
                'date': price.date_recorded,
                'price': price.price,
                'price_per_kg': price.price_per_kg
            })
            
            # Update photo URL if current entry has one but stored metadata doesn't
            if not product_data[key]['metadata']['photo_url'] and hasattr(price, 'image_path') and price.image_path:
                product_data[key]['metadata']['photo_url'] = price.image_path
        
        # Sort each product's data by date
        for key in product_data:
            product_data[key]['points'].sort(key=lambda x: x['date'])
        
        # Calculate price changes and analytics
        analytics_data = []
        
        for key, data in product_data.items():
            data_points = data['points']
            metadata = data['metadata']
            
            if len(data_points) < 2:
                continue  # Skip products with only one data point
                
            # Calculate daily changes
            changes = []
            percent_changes = []
            
            for i in range(1, len(data_points)):
                prev_price = data_points[i-1]['price']
                curr_price = data_points[i]['price']
                
                if prev_price and curr_price:  # Ensure both prices exist
                    change = curr_price - prev_price
                    percent_change = (change / prev_price * 100) if prev_price else 0
                    
                    days_diff = (data_points[i]['date'] - data_points[i-1]['date']).days
                    if days_diff > 0:  # Normalize to daily change if days between recordings > 1
                        change = change / days_diff
                        percent_change = percent_change / days_diff
                    
                    changes.append(change)
                    percent_changes.append(percent_change)
            
            # Extract price and date pairs
            price_date_pairs = [(point['price'], point['date']) for point in data_points if point['price'] is not None]

            if price_date_pairs:
                prices_only = [p for p, _ in price_date_pairs]
                max_price = max(prices_only)
                min_price = min(prices_only)
                max_min_diff = max_price - min_price
                max_min_percent_diff = ((max_price - min_price) / min_price * 100) if min_price else 0

                # Find dates
                max_price_date = next(date for price, date in price_date_pairs if price == max_price)
                min_price_date = next(date for price, date in price_date_pairs if price == min_price)
            else:
                max_price = min_price = max_min_diff = max_min_percent_diff = 0
                max_price_date = min_price_date = None

            if changes:  # Only add analytics if we have changes
                analytics_data.append({
                    'uid': metadata['uid'] if metadata['uid'] else "Generated from attributes",
                    'country': metadata['country'],
                    'company': metadata['company'],
                    'product_type': metadata['product_type'],
                    'product_subtype': metadata['product_subtype'],
                    'type': metadata['type'] if metadata['type'] else determine_bottle_type(metadata['product_subtype'], metadata['weight']),
                    'name': metadata['name'],
                    'weight': metadata['weight'],
                    'photo_url': metadata['photo_url'],
                    'first_date': data_points[0]['date'],
                    'last_date': data_points[-1]['date'],
                    'first_price': data_points[0]['price'],
                    'last_price': data_points[-1]['price'],
                    'total_change': data_points[-1]['price'] - data_points[0]['price'],
                    'total_percent_change': ((data_points[-1]['price'] - data_points[0]['price']) / data_points[0]['price'] * 100) if data_points[0]['price'] else 0,
                    'avg_daily_change': sum(changes) / len(changes) if changes else 0,
                    'avg_daily_percent_change': sum(percent_changes) / len(percent_changes) if percent_changes else 0,
                    'max_daily_increase': max(changes) if changes else 0,
                    'max_daily_decrease': min(changes) if changes else 0,
                    'max_min_price_diff': max_min_diff,
                    'max_min_percent_diff': max_min_percent_diff,
                    'max_price_date': max_price_date,
                    'min_price_date': min_price_date,
                    'data_points': len(data_points)
                })
        
        # Add headers to analytics sheet
        analytics_headers = [
            'Product Image', 'UID/Identifier', 'Country', 'Company', 'Product Type', 'Product Subtype', 'Type', 'Name', 'Weight',
            'First Date', 'Last Date', 'First Price', 'Last Price', 'Total Change',
            'Total % Change', 'Avg Daily Change', 'Avg Daily % Change',
            'Max Daily Increase', 'Max Daily Decrease', 'Max-Min Price Diff',
            'Max-Min % Diff', 'Max Price Date', 'Min Price Date', 'Data Points'
        ]
        
        for col, header in enumerate(analytics_headers):
            analytics_sheet.write(0, col, header)
            
        # Create a format to fit images properly
        row_height = 75  # Adjust as needed
        analytics_sheet.set_default_row(row_height)
        
        # Add analytics data
        for row, item in enumerate(analytics_data, 1):
            col = 0
            
            # Insert image if available (column 0)
            if item['photo_url']:
                try:
                    # Convert URL path to local static file path - handle both formats
                    image_path = item['photo_url']
                    if image_path.startswith('/static/'):
                        image_path = image_path[1:]  # Remove leading slash
                    elif not image_path.startswith('static/'):
                        image_path = image_path.split('/')[-1]  # Get the filename
                        image_path = f"static/media/{image_path}"
                    
                    logger.info(f"Attempting to load image from: {image_path}")
                    
                    if os.path.exists(image_path):
                        target_height = int(row_height * 1.33)  # Set target height for the image

                        # Open the image
                        pil_img = Image.open(image_path)
                        
                        # Calculate the width to maintain aspect ratio
                        original_width, original_height = pil_img.size
                        aspect_ratio = original_width / original_height
                        new_width = int(target_height * aspect_ratio)
                        
                        # Resize the image while maintaining aspect ratio
                        pil_img = pil_img.resize((new_width, target_height), Image.LANCZOS)
                        
                        # Save the resized image to a buffer
                        img_buffer = io.BytesIO()
                        pil_img.save(img_buffer, format=pil_img.format or 'PNG')
                        img_buffer.seek(0)
                        
                        # Insert image using xlsxwriter's insert_image method with a file-like object
                        analytics_sheet.insert_image(
                            row, col,
                            image_path,
                            {'image_data': img_buffer}
                        )
                        logger.info(f"Successfully inserted image for row {row}")
                    else:
                        logger.error(f"Image file not found at path: {image_path}")
                        analytics_sheet.write(row, col, f"Image not found at: {image_path}")
                except Exception as e:
                    logger.error(f"Error inserting image: {str(e)}")
                    analytics_sheet.write(row, col, f"Error with image: {item['photo_url']}")
            else:
                analytics_sheet.write(row, col, "No photo URL available")
            
            col += 1

            date_format = workbook.add_format({'num_format': 'dd-mm-yyyy'})
            
            # Continue with other data columns
            analytics_sheet.write(row, col, item['uid']); col += 1
            analytics_sheet.write(row, col, item['country']); col += 1
            analytics_sheet.write(row, col, item['company']); col += 1
            analytics_sheet.write(row, col, item['product_type']); col += 1
            analytics_sheet.write(row, col, item['product_subtype']); col += 1
            analytics_sheet.write(row, col, item['type']); col += 1
            analytics_sheet.write(row, col, item['name']); col += 1
            analytics_sheet.write(row, col, item['weight']); col += 1
            analytics_sheet.write(row, col, item['first_date'], date_format); col += 1
            analytics_sheet.write(row, col, item['last_date'], date_format); col += 1
            analytics_sheet.write(row, col, item['first_price']); col += 1
            analytics_sheet.write(row, col, item['last_price']); col += 1
            analytics_sheet.write(row, col, item['total_change']); col += 1
            analytics_sheet.write(row, col, item['total_percent_change']); col += 1
            analytics_sheet.write(row, col, item['avg_daily_change']); col += 1
            analytics_sheet.write(row, col, item['avg_daily_percent_change']); col += 1
            analytics_sheet.write(row, col, item['max_daily_increase']); col += 1
            analytics_sheet.write(row, col, item['max_daily_decrease']); col += 1
            analytics_sheet.write(row, col, item['max_min_price_diff']); col += 1
            analytics_sheet.write(row, col, item['max_min_percent_diff']); col += 1
            analytics_sheet.write(row, col, item['max_price_date'], date_format); col += 1
            analytics_sheet.write(row, col, item['min_price_date'], date_format); col += 1
            analytics_sheet.write(row, col, item['data_points'])
        
        # Create a summary sheet
        summary_sheet = workbook.add_worksheet("Summary")
        
        # Calculate summary statistics
        total_products = len(analytics_data)
        avg_price_change = sum(item['total_change'] for item in analytics_data) / total_products if total_products else 0
        avg_percent_change = sum(item['total_percent_change'] for item in analytics_data) / total_products if total_products else 0
        avg_daily_change = sum(item['avg_daily_change'] for item in analytics_data) / total_products if total_products else 0
        avg_daily_percent_change = sum(item['avg_daily_percent_change'] for item in analytics_data) / total_products if total_products else 0
        
        # Find products with highest increases and decreases
        analytics_data.sort(key=lambda x: x['total_percent_change'], reverse=True)
        top_increases = analytics_data[:5] if len(analytics_data) >= 5 else analytics_data
        
        analytics_data.sort(key=lambda x: x['total_percent_change'])
        top_decreases = analytics_data[:5] if len(analytics_data) >= 5 else analytics_data
        
        # Add summary data
        summary_sheet.write(0, 0, "Report Summary")
        summary_sheet.write(1, 0, "Date Range:")
        summary_sheet.write(1, 1, f"{start_date or 'All'} to {end_date or 'All'}")

        row_offset = 2
        if country_list:
            summary_sheet.write(row_offset, 0, "Countries:")
            summary_sheet.write(row_offset, 1, ', '.join(country_list))
            row_offset += 1

        if company_list:
            summary_sheet.write(row_offset, 0, "Companies:")
            summary_sheet.write(row_offset, 1, ', '.join(company_list))
            row_offset += 1

        if product_type_list:
            summary_sheet.write(row_offset, 0, "Product Types:")
            summary_sheet.write(row_offset, 1, ', '.join(product_type_list))
            row_offset += 1

        summary_sheet.write(row_offset + 1, 0, "Total Products Analyzed:")
        summary_sheet.write(row_offset + 1, 1, total_products)

        summary_sheet.write(row_offset + 2, 0, "Average Total Price Change:")
        summary_sheet.write(row_offset + 2, 1, avg_price_change)

        summary_sheet.write(row_offset + 3, 0, "Average Total Percent Change:")
        summary_sheet.write(row_offset + 3, 1, avg_percent_change)

        summary_sheet.write(row_offset + 4, 0, "Average Daily Price Change:")
        summary_sheet.write(row_offset + 4, 1, avg_daily_change)

        summary_sheet.write(row_offset + 5, 0, "Average Daily Percent Change:")
        summary_sheet.write(row_offset + 5, 1, avg_daily_percent_change)
        
        # Add top increases section
        increases_start = row_offset + 7
        summary_sheet.write(increases_start, 0, "Products with Highest Price Increases:")
        summary_sheet.write(increases_start + 1, 0, "Product Name")
        summary_sheet.write(increases_start + 1, 1, "Company")
        summary_sheet.write(increases_start + 1, 2, "Total % Change")
        summary_sheet.write(increases_start + 1, 3, "Photo URL")

        for i, item in enumerate(top_increases, increases_start + 2):
            summary_sheet.write(i, 0, item['name'])
            summary_sheet.write(i, 1, item['company'])
            summary_sheet.write(i, 2, item['total_percent_change'])
            if item['photo_url']:
                summary_sheet.write(i, 3, item['photo_url'])

        # Add top decreases section
        decreases_start = increases_start + 2 + len(top_increases) + 1
        summary_sheet.write(decreases_start, 0, "Products with Highest Price Decreases:")
        summary_sheet.write(decreases_start + 1, 0, "Product Name")
        summary_sheet.write(decreases_start + 1, 1, "Company")
        summary_sheet.write(decreases_start + 1, 2, "Total % Change")
        summary_sheet.write(decreases_start + 1, 3, "Photo URL")

        for i, item in enumerate(top_decreases, decreases_start + 2):
            summary_sheet.write(i, 0, item['name'])
            summary_sheet.write(i, 1, item['company'])
            summary_sheet.write(i, 2, item['total_percent_change'])
            if item['photo_url']:
                summary_sheet.write(i, 3, item['photo_url'])
        
        # Add column width settings to improve readability
        # Set wider column for image URLs and product names
        analytics_sheet.set_column(0, 0, 30)  # Product Image column
        analytics_sheet.set_column(1, 1, 15)  # UID column
        analytics_sheet.set_column(7, 7, 30)  # Name column
        
        # Create formats for analytics data
        header_format = workbook.add_format({
            'bold': True,
            'bg_color': '#D9EAD3',  # Light green
            'border': 1
        })
        
        percent_format = workbook.add_format({'num_format': '0.00%'})
        currency_format = workbook.add_format({'num_format': '€#,##0.00'})
        
        # Apply formats to headers
        for col, _ in enumerate(analytics_headers):
            analytics_sheet.write(0, col, analytics_headers[col], header_format)
        
        # Apply formats to appropriate columns
        for row in range(1, len(analytics_data) + 1):
            analytics_sheet.write(row, 13, analytics_data[row-1]['total_percent_change'] / 100, percent_format)
            analytics_sheet.write(row, 15, analytics_data[row-1]['avg_daily_percent_change'] / 100, percent_format);
            
            # Currency format for price columns
            analytics_sheet.write(row, 11, analytics_data[row-1]['first_price'], currency_format)
            analytics_sheet.write(row, 12, analytics_data[row-1]['last_price'], currency_format)
            analytics_sheet.write(row, 13, analytics_data[row-1]['total_change'], currency_format)
            analytics_sheet.write(row, 14, analytics_data[row-1]['avg_daily_change'], currency_format)
            analytics_sheet.write(row, 16, analytics_data[row-1]['max_daily_increase'], currency_format)
            analytics_sheet.write(row, 17, analytics_data[row-1]['max_daily_decrease'], currency_format)
        
        #Create graph sheet
        graphs_sheet = workbook.add_worksheet("Graphs")

        graph_header_format = workbook.add_format({
            'bold': True,
            'size': 16
        })

        # Create a chart using generate_price_barplot function
        # Convert prices to DataFrame and get latest values for each unique light butane product

        graphs_sheet.write('A1',"Latest Average Prices per Country (All Types)", graph_header_format)
        graphs_sheet.write('A24',"Latest Average Prices per Country (Light Bottles)", graph_header_format)
        graphs_sheet.write('A47',"Latest Average Prices per Country (Regular Bottles)", graph_header_format)

        propane_df = pd.DataFrame([vars(p) for p in prices if p.product_type == 'Propane'])
        if not propane_df.empty:
            # Sort by date and get latest record for each unique product
            propane_df['date_recorded'] = pd.to_datetime(propane_df['date_recorded'])
            propane_df = propane_df.sort_values('date_recorded').groupby(['company', 'name', 'weight']).last().reset_index()
        
        propane_df.head(100)
        
        if not propane_df.empty:
            chart_img = generate_price_barplot(
                propane_df,
                value_col='price_per_kg',
                title='Average Price per KG (Propane)',
                flag_dir= 'static/media'
            )
            graphs_sheet.insert_image('M2', 'chart1.png', {'image_data': chart_img, 'x_scale': 0.5, 'y_scale': 0.5})

        butane_df = pd.DataFrame([vars(p) for p in prices if p.product_type == 'Butane'])
        if not butane_df.empty:
            # Sort by date and get latest record for each unique product
            butane_df['date_recorded'] = pd.to_datetime(butane_df['date_recorded'])
            butane_df = butane_df.sort_values('date_recorded').groupby(['company', 'name', 'weight']).last().reset_index()
        
        butane_df.head(100)
        
        if not butane_df.empty:
            chart_img = generate_price_barplot(
                butane_df,
                value_col='price_per_kg',
                title='Average Price per KG (Butane)',
                flag_dir= 'static/media'
            )
            graphs_sheet.insert_image('A2', 'chart2.png', {'image_data': chart_img, 'x_scale': 0.5, 'y_scale': 0.5})

        light_butane_df = pd.DataFrame([vars(p) for p in prices if p.product_type == 'Butane' and p.product_subtype == 'Light'])
        if not light_butane_df.empty:
            # Sort by date and get latest record for each unique product
            light_butane_df['date_recorded'] = pd.to_datetime(light_butane_df['date_recorded'])
            light_butane_df = light_butane_df.sort_values('date_recorded').groupby(['company', 'name', 'weight']).last().reset_index()

        print(light_butane_df)
        
        if not light_butane_df.empty:
            chart_img = generate_price_barplot(
                light_butane_df,
                value_col='price_per_kg',
                title='Average Price per KG (Light Butane)',
                flag_dir= 'static/media'
            )
            graphs_sheet.insert_image('A25', 'chart3.png', {'image_data': chart_img, 'x_scale': 0.5, 'y_scale': 0.5})

        light_propane_df = pd.DataFrame([vars(p) for p in prices if p.product_type == 'Propane' and p.product_subtype == 'Light'])
        if not light_propane_df.empty:
            # Sort by date and get latest record for each unique product
            light_propane_df['date_recorded'] = pd.to_datetime(light_propane_df['date_recorded'])
            light_propane_df = light_propane_df.sort_values('date_recorded').groupby(['company', 'name', 'weight']).last().reset_index()

        print(light_propane_df)
        
        if not light_propane_df.empty:
            chart_img = generate_price_barplot(
                light_propane_df,
                value_col='price_per_kg',
                title='Average Price per KG (Light Propane)',
                flag_dir= 'static/media'
            )
            graphs_sheet.insert_image('M25', 'chart4.png', {'image_data': chart_img, 'x_scale': 0.5, 'y_scale': 0.5})

        regular_butane_df = pd.DataFrame([vars(p) for p in prices if p.product_type == 'Butane'])
        if not regular_butane_df.empty:
            # Sort by date and get latest record for each unique product
            regular_butane_df['date_recorded'] = pd.to_datetime(regular_butane_df['date_recorded'])
            regular_butane_df = regular_butane_df.sort_values('date_recorded').groupby(['company', 'name', 'weight']).last().reset_index()

        print(regular_butane_df)
        
        if not regular_butane_df.empty:
            chart_img = generate_price_barplot(
                regular_butane_df,
                value_col='price_per_kg',
                title='Average Price per KG (Regular Butane)',
                flag_dir= 'static/media'
            )
            graphs_sheet.insert_image('A48', 'chart5.png', {'image_data': chart_img, 'x_scale': 0.5, 'y_scale': 0.5})

        regular_propane_df = pd.DataFrame([vars(p) for p in prices if p.product_type == 'Propane'])
        if not regular_propane_df.empty:
            # Sort by date and get latest record for each unique product
            regular_propane_df['date_recorded'] = pd.to_datetime(regular_propane_df['date_recorded'])
            regular_propane_df = regular_propane_df.sort_values('date_recorded').groupby(['company', 'name', 'weight']).last().reset_index()

        print(regular_propane_df)
        
        if not regular_propane_df.empty:
            chart_img = generate_price_barplot(
                regular_propane_df,
                value_col='price_per_kg',
                title='Average Price per KG (Regular Propane)',
                flag_dir= 'static/media'
            )
            graphs_sheet.insert_image('M48', 'chart6.png', {'image_data': chart_img, 'x_scale': 0.5, 'y_scale': 0.5})

        workbook.close()
        output.seek(0)

        # Generate filename with current timestamp
        filename = f"gas_prices_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"

        # Save a temporary file to send as an attachment
        temp_file_path = os.path.join(settings.RESULTS_DIR, filename)
        with open(temp_file_path, "wb") as f:
            f.write(output.getvalue())

        # Return HTML with download trigger
        download_url = f"data:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;base64,{base64.b64encode(output.getvalue()).decode()}"

        # Send email notification about report generation
        try:
            email_service = EmailService()
            email_service.send_email(
                subject="Gas Prices Report - Generated",
                body="Please find the attached gas prices report.",
                attachments=[temp_file_path]
            )
            logger.info("Report generation email sent successfully")
        except Exception as e:
            logger.error(f"Failed to send report generation email: {str(e)}")

        return HTMLResponse(
            f"""
            <a
                href="{download_url}"
                download="{filename}"
                id="download-link"
                class="hidden"
            ></a>
            <script>
                document.getElementById('download-link').click();

                // Trigger custom event for toast notification
                if (typeof window.toastManager !== 'undefined') {{
                    window.toastManager.success('Report Generated', 'Excel report with {len(prices)} records has been downloaded successfully.');
                }}
            </script>
            """
        )

    except Exception as e:
        logger.error(f"Error generating report: {str(e)}")
        return HTMLResponse(
            f"""
            <script>
                if (typeof window.toastManager !== 'undefined') {{
                    window.toastManager.error('Report Generation Failed', 'There was an error generating the report: {str(e)}');
                }}
            </script>
            """,
            status_code=500
        )


@app.get("/generate-email-report")
async def generate_email_report(
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    # Legacy single-select parameters (for backward compatibility)
    country: Optional[str] = None,
    company: Optional[str] = None,
    product_type: Optional[str] = None,
    # New multi-select parameters
    countries: Optional[str] = None,
    companies: Optional[str] = None,
    product_types: Optional[str] = None
):
    """Generate and send an email report with analytical data based on filtered data"""
    try:
        # Parse multi-select parameters (prioritize new multi-select over legacy single-select)
        country_list = countries.split(',') if countries else ([country] if country else None)
        company_list = companies.split(',') if companies else ([company] if company else None)
        product_type_list = product_types.split(',') if product_types else ([product_type] if product_type else None)

        # Get filtered data using multi-select parameters
        prices = get_gas_prices(
            start_date=datetime.fromisoformat(start_date) if start_date else None,
            end_date=datetime.fromisoformat(end_date) if end_date else None,
            countries=country_list,
            companies=company_list,
            product_types=product_type_list
        )

        if not prices:
            return HTMLResponse(
                """
                <div class="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded">
                    <strong>No Data Found:</strong> No data matches the selected filters. Please adjust your filters and try again.
                </div>
                """
            )

        # Calculate analytics for email report
        country_analytics = {}
        total_sites = len(set(f"{p.country}_{p.company}" for p in prices))
        successful_scans = len(prices)

        # Group data by country for analytics
        for price in prices:
            country = price.country
            if country not in country_analytics:
                country_analytics[country] = {
                    'prices': [],
                    'companies': set(),
                    'product_types': set()
                }

            country_analytics[country]['prices'].append(price.price)
            country_analytics[country]['companies'].add(price.company)
            country_analytics[country]['product_types'].add(price.product_type)

        # Calculate country summary data
        country_data = []
        for country, data in country_analytics.items():
            avg_price = sum(data['prices']) / len(data['prices']) if data['prices'] else 0

            # Calculate price change (simplified - compare first and last price if multiple entries)
            price_change = "N/A"
            if len(data['prices']) > 1:
                sorted_prices = sorted(data['prices'])
                change_percent = ((sorted_prices[-1] - sorted_prices[0]) / sorted_prices[0] * 100) if sorted_prices[0] > 0 else 0
                price_change = f"{change_percent:+.1f}%"

            country_data.append({
                'name': country,
                'avg_price': f"€{avg_price:.2f}",
                'price_change': price_change,
                'companies': len(data['companies']),
                'product_types': len(data['product_types'])
            })

        # Generate alerts based on data analysis
        alerts = []

        # Check for significant price variations
        all_prices = [p.price for p in prices if p.price]
        if all_prices:
            avg_price = sum(all_prices) / len(all_prices)
            max_price = max(all_prices)
            min_price = min(all_prices)

            if (max_price - min_price) / avg_price > 0.3:  # 30% variation
                alerts.append(f"High price variation detected: €{min_price:.2f} - €{max_price:.2f}")

        # Check for countries with limited data
        for country, data in country_analytics.items():
            if len(data['companies']) == 1:
                alerts.append(f"Limited competition in {country}: Only {list(data['companies'])[0]} data available")

        # Create filter summary for the report
        filter_summary = []
        if start_date or end_date:
            date_range = f"{start_date or 'All'} to {end_date or 'All'}"
            filter_summary.append(f"Date Range: {date_range}")

        if country_list:
            filter_summary.append(f"Countries: {', '.join(country_list)}")

        if company_list:
            filter_summary.append(f"Companies: {', '.join(company_list)}")

        if product_type_list:
            filter_summary.append(f"Product Types: {', '.join(product_type_list)}")

        collection_period = '; '.join(filter_summary) if filter_summary else 'All available data'

        # Prepare report data for email template
        report_data = {
            'recipient_name': 'Gas Price Analyst',
            'total_sites': total_sites,
            'successful_scans': successful_scans,
            'collection_period': collection_period,
            'country_data': country_data,
            'alerts': alerts,
            'applied_filters': {
                'countries': country_list,
                'companies': company_list,
                'product_types': product_type_list,
                'start_date': start_date,
                'end_date': end_date
            }
        }

        # Generate Excel attachment with the same filters
        # (We'll reuse the existing Excel generation logic)
        from io import BytesIO
        import xlsxwriter

        output = BytesIO()
        workbook = xlsxwriter.Workbook(output)

        # Create a simple summary sheet for email attachment
        summary_sheet = workbook.add_worksheet("Email Report Summary")

        # Add summary data
        summary_sheet.write(0, 0, "Email Report Summary")
        summary_sheet.write(1, 0, "Generated:")
        summary_sheet.write(1, 1, datetime.now().strftime('%Y-%m-%d %H:%M:%S'))

        summary_sheet.write(3, 0, "Applied Filters:")
        row = 4
        for filter_item in filter_summary:
            summary_sheet.write(row, 0, filter_item)
            row += 1

        row += 1
        summary_sheet.write(row, 0, "Country Summary:")
        row += 1
        summary_sheet.write(row, 0, "Country")
        summary_sheet.write(row, 1, "Avg Price")
        summary_sheet.write(row, 2, "Price Change")
        summary_sheet.write(row, 3, "Companies")
        summary_sheet.write(row, 4, "Product Types")

        for country in country_data:
            row += 1
            summary_sheet.write(row, 0, country['name'])
            summary_sheet.write(row, 1, country['avg_price'])
            summary_sheet.write(row, 2, country['price_change'])
            summary_sheet.write(row, 3, country['companies'])
            summary_sheet.write(row, 4, country['product_types'])

        workbook.close()
        output.seek(0)

        # Save temporary attachment file
        settings = get_settings()
        filename = f"email_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        temp_file_path = os.path.join(settings.RESULTS_DIR, filename)
        with open(temp_file_path, "wb") as f:
            f.write(output.getvalue())

        # Send email report
        email_service = EmailService()
        email_service.send_report_email(report_data, attachments=[temp_file_path])

        return HTMLResponse(
            f"""
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
                <strong>Email Report Sent!</strong>
                <p>A comprehensive email report has been generated and sent with the following data:</p>
                <ul class="list-disc list-inside mt-2">
                    <li>Total Sites: {total_sites}</li>
                    <li>Data Points: {successful_scans}</li>
                    <li>Countries: {len(country_data)}</li>
                    <li>Applied Filters: {collection_period}</li>
                </ul>
                <p class="mt-2">The report includes an Excel attachment with detailed analytics.</p>
            </div>
            <script>
                if (typeof window.toastManager !== 'undefined') {{
                    window.toastManager.success('Email Sent Successfully', 'Report with {successful_scans} data points has been sent to your email address.');
                }}
            </script>
            """
        )

    except Exception as e:
        logger.error(f"Error generating email report: {str(e)}")
        return HTMLResponse(
            f"""
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                <strong>Error:</strong> Failed to generate email report. {str(e)}
            </div>
            <script>
                if (typeof window.toastManager !== 'undefined') {{
                    window.toastManager.error('Email Failed', 'There was an error sending the email report: {str(e)}');
                }}
            </script>
            """
        )