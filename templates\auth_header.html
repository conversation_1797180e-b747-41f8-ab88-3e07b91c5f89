<!-- Authentication Header Component -->
<!-- This component provides consistent authentication status display and logout functionality across all protected pages -->

<style>
    /* HTMX indicator styles for logout button */
    .htmx-indicator {
        display: none;
    }
    .htmx-request .htmx-indicator {
        display: inline;
    }
    .htmx-request.htmx-indicator {
        display: inline;
    }
</style>

<div class="auth-header bg-white border-b border-gray-200 shadow-sm mb-6">
    <div class="container mx-auto px-4 py-3">
        <div class="flex justify-between items-center">
            <!-- Left side: App branding and navigation -->
            <div class="flex items-center space-x-6">
                <div class="flex items-center space-x-2">
                    <svg class="w-8 h-8 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1V8z" clip-rule="evenodd"></path>
                    </svg>
                    <h1 class="text-xl font-bold text-blue-600">Gas Bottle Price Scraper</h1>
                </div>
                
                <!-- Navigation links -->
                <nav class="hidden md:flex space-x-4">
                    <a href="/manual-request" class="text-gray-600 hover:text-blue-600 font-medium transition-colors duration-200 px-3 py-2 rounded-md hover:bg-gray-100">
                        Manual Request
                    </a>
                    <a href="/admin" class="text-gray-600 hover:text-blue-600 font-medium transition-colors duration-200 px-3 py-2 rounded-md hover:bg-gray-100">
                        Admin
                    </a>
                    <a href="/results" class="text-gray-600 hover:text-blue-600 font-medium transition-colors duration-200 px-3 py-2 rounded-md hover:bg-gray-100">
                        Results
                    </a>
                    <a href="/database" class="text-gray-600 hover:text-blue-600 font-medium transition-colors duration-200 px-3 py-2 rounded-md hover:bg-gray-100">
                        Database
                    </a>
                </nav>
            </div>

            <!-- Right side: Authentication status and logout -->
            <div class="flex items-center space-x-4">
                {% if authenticated %}
                    <!-- Authentication status indicator -->
                    <div class="flex items-center space-x-3">
                        <!-- User info -->
                        <div class="flex items-center space-x-2 bg-green-50 border border-green-200 rounded-lg px-3 py-2">
                            <div class="flex items-center space-x-2">
                                <!-- User icon -->
                                <svg class="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                                </svg>
                                <!-- Status indicator -->
                                <div class="flex items-center space-x-1">
                                    <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                                    <span class="text-sm text-green-800 font-medium">Authenticated</span>
                                </div>
                            </div>
                            <!-- Username -->
                            <div class="border-l border-green-300 pl-2">
                                <span class="text-sm text-green-900 font-semibold">{{ username }}</span>
                            </div>
                        </div>

                        <!-- Logout button -->
                        <form method="post" action="/auth/logout" class="inline"
                              hx-post="/auth/logout"
                              hx-trigger="submit"
                              hx-swap="none">
                            <button
                                type="submit"
                                class="flex items-center space-x-2 bg-red-50 hover:bg-red-100 border border-red-200 hover:border-red-300 text-red-700 hover:text-red-800 font-medium px-4 py-2 rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
                                title="Sign out of your account"
                            >
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                                </svg>
                                <span class="text-sm">Logout</span>
                                <span class="htmx-indicator ml-2">
                                    <svg class="w-4 h-4 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                    </svg>
                                </span>
                            </button>
                        </form>
                    </div>
                {% else %}
                    <!-- Not authenticated state -->
                    <div class="flex items-center space-x-2 bg-yellow-50 border border-yellow-200 rounded-lg px-3 py-2">
                        <svg class="w-5 h-5 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="text-sm text-yellow-800 font-medium">Not Authenticated</span>
                        <a href="/login" class="text-sm text-blue-600 hover:text-blue-800 font-medium underline ml-2">Login</a>
                    </div>
                {% endif %}

                <!-- Mobile menu button (for future mobile navigation) -->
                <button 
                    type="button" 
                    class="md:hidden p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    onclick="toggleMobileMenu()"
                >
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                </button>
            </div>
        </div>

        <!-- Mobile navigation menu (hidden by default) -->
        <div id="mobile-menu" class="hidden md:hidden mt-4 pt-4 border-t border-gray-200">
            <nav class="flex flex-col space-y-2">
                <a href="/manual-request" class="text-gray-600 hover:text-blue-600 font-medium px-3 py-2 rounded-md hover:bg-gray-100">
                    Manual Request
                </a>
                <a href="/admin" class="text-gray-600 hover:text-blue-600 font-medium px-3 py-2 rounded-md hover:bg-gray-100">
                    Admin
                </a>
                <a href="/results" class="text-gray-600 hover:text-blue-600 font-medium px-3 py-2 rounded-md hover:bg-gray-100">
                    Results
                </a>
                <a href="/database" class="text-gray-600 hover:text-blue-600 font-medium px-3 py-2 rounded-md hover:bg-gray-100">
                    Database
                </a>
            </nav>
        </div>
    </div>
</div>

<script>
    // Mobile menu toggle function
    function toggleMobileMenu() {
        const mobileMenu = document.getElementById('mobile-menu');
        mobileMenu.classList.toggle('hidden');
    }

    // Handle HTMX logout redirect
    document.addEventListener('DOMContentLoaded', function() {
        // Listen for HTMX afterRequest event to handle logout redirect
        document.body.addEventListener('htmx:afterRequest', function(evt) {
            if (evt.detail.requestConfig.path === '/auth/logout') {
                // Check for redirect header
                const redirectUrl = evt.detail.xhr.getResponseHeader('HX-Redirect');
                if (redirectUrl) {
                    window.location.href = redirectUrl;
                }
            }
        });
    });
</script>
