2025-06-17 17:30:42,192 - app.services.email_service - INFO - Logging initialized
2025-06-17 17:30:42,195 - app.services.email_service - INFO - Email template directory: C:\Users\<USER>\Downloads\Rubis-Bottle-Data-codespace-laughing-happiness-vxr446x59xx3x4pq\Rubis-Bottle-Data-codespace-laughing-happiness-vxr446x59xx3x4pq\scripts\..\templates\email
2025-06-17 17:30:42,205 - app.services.email_service - INFO - Successfully rendered template: report_template.html
2025-06-17 17:30:42,206 - app.services.email_service - INFO - Email template directory: C:\Users\<USER>\Downloads\Rubis-Bottle-Data-codespace-laughing-happiness-vxr446x59xx3x4pq\Rubis-Bottle-Data-codespace-laughing-happiness-vxr446x59xx3x4pq\scripts\..\templates\email
2025-06-17 17:32:46,784 - app.services.email_service - INFO - Logging initialized
2025-06-17 17:32:46,787 - app.services.email_service - INFO - Email template directory: C:\Users\<USER>\Downloads\Rubis-Bottle-Data-codespace-laughing-happiness-vxr446x59xx3x4pq\Rubis-Bottle-Data-codespace-laughing-happiness-vxr446x59xx3x4pq\scripts\..\templates\email
2025-06-17 17:32:46,794 - app.services.email_service - INFO - Successfully rendered template: report_template.html
2025-06-17 17:32:46,797 - app.services.email_service - INFO - Email template directory: C:\Users\<USER>\Downloads\Rubis-Bottle-Data-codespace-laughing-happiness-vxr446x59xx3x4pq\Rubis-Bottle-Data-codespace-laughing-happiness-vxr446x59xx3x4pq\scripts\..\templates\email
2025-06-17 17:33:29,214 - app.services.email_service - INFO - Logging initialized
2025-06-17 17:33:29,218 - app.services.email_service - INFO - Email template directory: C:\Users\<USER>\Downloads\Rubis-Bottle-Data-codespace-laughing-happiness-vxr446x59xx3x4pq\Rubis-Bottle-Data-codespace-laughing-happiness-vxr446x59xx3x4pq\scripts\..\templates\email
2025-06-17 17:33:29,225 - app.services.email_service - INFO - Successfully rendered template: report_template.html
2025-06-17 17:33:29,229 - app.services.email_service - INFO - Successfully rendered template: report_template.html
2025-06-17 17:33:30,564 - app.services.email_service - INFO - Email <NAME_EMAIL> with subject: Gas Price Report - 2025-06-17
2025-06-17 17:33:30,600 - app.services.email_service - INFO - Report email sent successfully with 0 attachments
2025-06-17 17:33:30,601 - app.services.email_service - INFO - Email template directory: C:\Users\<USER>\Downloads\Rubis-Bottle-Data-codespace-laughing-happiness-vxr446x59xx3x4pq\Rubis-Bottle-Data-codespace-laughing-happiness-vxr446x59xx3x4pq\scripts\..\templates\email
2025-06-18 09:54:54,698 - app.services.email_service - INFO - Logging initialized
2025-06-18 09:54:54,711 - app.db.database - INFO - Successfully saved 5 records to database
2025-06-18 09:54:54,903 - app.services.email_service - INFO - Email template directory: C:\Users\<USER>\Downloads\Rubis-Bottle-Data-codespace-laughing-happiness-vxr446x59xx3x4pq\Rubis-Bottle-Data-codespace-laughing-happiness-vxr446x59xx3x4pq\scripts\..\templates\email
2025-06-18 09:54:54,922 - app.services.email_service - INFO - Successfully rendered template: report_template.html
2025-06-18 10:21:14,699 - app.services.email_service - INFO - Logging initialized
2025-06-18 10:21:14,926 - httpx - INFO - HTTP Request: GET http://testserver/database "HTTP/1.1 200 OK"
2025-06-18 10:21:15,275 - httpx - INFO - HTTP Request: GET http://testserver/manual-request "HTTP/1.1 200 OK"
2025-06-18 10:21:15,380 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/PT_REPSOL_11_PRO.png
2025-06-18 10:21:15,381 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/PT_REPSOL_13_BUT.png
2025-06-18 10:21:15,381 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/PT_REPSOL_12-5_BUT.png
2025-06-18 10:21:15,381 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/PT_REPSOL_K11_BUT.png
2025-06-18 10:21:15,381 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/PT_RUBIS_45_PRO.png
2025-06-18 10:21:15,382 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/PT_RUBIS_11_PRO.png
2025-06-18 10:21:15,382 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/PT_RUBIS_13_BUT.png
2025-06-18 10:21:15,382 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/PT_RUBIS_L12_BUT.png
2025-06-18 10:21:15,382 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/IT_BUTANGAS_25_PRO.png
2025-06-18 10:21:15,382 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/IT_BUTANGAS_20_PRO.png
2025-06-18 10:21:15,382 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/IT_BUTANGAS_15_PRO.png
2025-06-18 10:21:15,382 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/IT_BUTANGAS_10_PRO.png
2025-06-18 10:21:15,383 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/IT_LIQUIGAS_25_PRO.jpg
2025-06-18 10:21:15,383 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/IT_LIQUIGAS_20_PRO.jpg
2025-06-18 10:21:15,383 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/IT_LIQUIGAS_15_PRO.jpg
2025-06-18 10:21:15,383 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/IT_LIQUIGAS_10_PRO.jpg
2025-06-18 10:21:15,383 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/IT_ENI_25_PRO.jpeg
2025-06-18 10:21:15,384 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/IT_ENI_15_PRO.jpeg
2025-06-18 10:21:15,384 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/IT_ENI_10_PRO.jpeg
2025-06-18 10:21:15,384 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/DE_ALUGAS_11_PRO.jpeg
2025-06-18 10:21:15,384 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/DE_GLOBALGAS_33_PRO.jpeg
2025-06-18 10:21:15,384 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/DE_GLOBALGAS_11_PRO.jpeg
2025-06-18 10:21:15,384 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/DE_HOYER_33_PRO.jpeg
2025-06-18 10:21:15,384 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/DE_HOYER_11_PRO.jpeg
2025-06-18 10:21:15,384 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/DE_WESTFALENGAS_33_PRO.jpeg
2025-06-18 10:21:15,384 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/DE_WESTFALENGAS_11_PRO.jpg
2025-06-18 10:21:15,384 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/DE_TYCKZA_33_PRO.jpg
2025-06-18 10:21:15,385 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/DE_TYCKZA_11_PRO.jpeg
2025-06-18 10:21:15,385 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/FR_VITOGAZ_35_PRO.png
2025-06-18 10:21:15,385 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/FR_VITOGAZ_13_PRO.png
2025-06-18 10:21:15,385 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/FR_VITOGAZ_13_BUT.png
2025-06-18 10:21:15,385 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/FR_UNERGIES_13_PRO.png
2025-06-18 10:21:15,385 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/FR_UNERGIES_13_BUT.png
2025-06-18 10:21:15,385 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/FR_PRIMAGAZ_35_PRO.jpeg
2025-06-18 10:21:15,386 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/FR_PRIMAGAZ_13_PRO.jpeg
2025-06-18 10:21:15,386 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/FR_PRIMAGAZ_13_BUT.jpeg
2025-06-18 10:21:15,386 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/FR_EHO_35_PRO.png
2025-06-18 10:21:15,386 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/FR_EHO_13_PRO.png
2025-06-18 10:21:15,386 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/FR_EHO_12-5_BUT.png
2025-06-18 10:21:15,386 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/FR_CLAIRGAZ_11_PRO.jpg
2025-06-18 10:21:15,386 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/FR_CLAIRGAZ_13_BUT.jpg
2025-06-18 10:21:15,386 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/FR_CARREFOUR_13_BUT.jpg
2025-06-18 10:21:15,386 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/FR_BUTAGAZ_35_PRO.png
2025-06-18 10:21:15,386 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/FR_BUTAGAZ_13_PRO.png
2025-06-18 10:21:15,386 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/FR_BUTAGAZ_L10_BUT.png
2025-06-18 10:21:15,386 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/FR_BUTAGAZ_13_BUT.png
2025-06-18 10:21:15,387 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/FR_AUCHAN_13_PRO.png
2025-06-18 10:21:15,387 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/FR_AUCHAN_13_BUT.png
2025-06-18 10:21:15,387 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/FR_ANTARGAZ_35_PRO.png
2025-06-18 10:21:15,387 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/FR_ANTARGAZ_13_PRO.png
2025-06-18 10:21:15,387 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/FR_ANTARGAZ_L10_BUT.png
2025-06-18 10:21:15,387 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/FR_ANTARGAZ_13_BUT.png
2025-06-18 10:21:15,387 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/UK_FLOGAS_47_PRO.jpeg
2025-06-18 10:21:15,387 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/UK_FLOGAS_19_PRO.jpeg
2025-06-18 10:21:15,387 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/UK_FLOGAS_13(21mm)_BUT.jpg
2025-06-18 10:21:15,387 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/UK_FLOGAS_13(20mm)_BUT.jpg
2025-06-18 10:21:15,387 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/UK_FLOGAS_11_PRO.jpeg
2025-06-18 10:21:15,387 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/UK_FLOGAS_L10_PRO.png
2025-06-18 10:21:15,387 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/UK_EXTRAGAS_46_PRO.png
2025-06-18 10:21:15,387 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/UK_EXTRAGAS_19_PRO.png
2025-06-18 10:21:15,388 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/UK_EXTRAGAS_11_PRO.png
2025-06-18 10:21:15,388 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/UK_EXTRAGAS_12_BUT.png
2025-06-18 10:21:15,388 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/UK_CALOR_47_PRO.jpeg
2025-06-18 10:21:15,388 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/UK_CALOR_19_PRO.jpeg
2025-06-18 10:21:15,388 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/UK_CALOR_13_PRO.jpeg
2025-06-18 10:21:15,388 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/UK_CALOR_15_BUT.jpeg
2025-06-18 10:21:15,388 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/CH_FLAGA_35_PRO.jpeg
2025-06-18 10:21:15,388 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/CH_FLAGA_L10_PRO.jpeg
2025-06-18 10:21:15,388 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/CH_FLAGA_10-5_PRO.jpeg
2025-06-18 10:21:15,388 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/CH_GLOBALGAS_33_PRO.jpeg
2025-06-18 10:21:15,388 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/CH_GLOBALGAS_11_PRO.jpeg
2025-06-18 10:21:15,388 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/CH_VITOGAZ_35_PRO.jpeg
2025-06-18 10:21:15,389 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/CH_VITOGAZ_10-5_PRO.jpeg
2025-06-18 10:21:15,389 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/ES_REPSOL_L12_BUT.jpeg
2025-06-18 10:21:15,389 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/ES_REPSOL_35_PRO.jpeg
2025-06-18 10:21:15,389 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/ES_CEPSA_11_PRO.jpeg
2025-06-18 10:21:15,389 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/ES_CEPSA_L11_PRO.jpeg
2025-06-18 10:21:15,389 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/ES_CEPSA_L12-5_BUT.jpeg
2025-06-18 10:21:15,389 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/ES_GALP_35_PRO.png
2025-06-18 10:21:15,389 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/ES_GALP_11_PRO.png
2025-06-18 10:21:15,389 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/ES_GALP_P12_BUT.png
2025-06-18 10:21:15,389 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/ES_GALP_12.5_BUT.png
2025-06-18 10:21:15,389 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/PT_TUTIGAS_45_PRO.jpeg
2025-06-18 10:21:15,389 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/PT_TUTIGAS_11_PRO.jpeg
2025-06-18 10:21:15,389 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/PT_TUTIGAS_13_BUT.jpeg
2025-06-18 10:21:15,390 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/PT_TUTIGAS_XL12-5_BUT.png
2025-06-18 10:21:15,390 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/PT_PRIO_45_PRO.jpeg
2025-06-18 10:21:15,390 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/PT_PRIO_9_BUT.jpeg
2025-06-18 10:21:15,390 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/PT_OZ_45_PRO.png
2025-06-18 10:21:15,390 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/PT_OZ_11_PRO.png
2025-06-18 10:21:15,390 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/PT_OZ_13_BUT.png
2025-06-18 10:21:15,390 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/PT_OZ_P13_BUT.png
2025-06-18 10:21:15,390 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/PT_GALP_45_PRO.png
2025-06-18 10:21:15,390 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/PT_GALP_11_PRO.png
2025-06-18 10:21:15,390 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/PT_GALP_13_BUT.png
2025-06-18 10:21:15,390 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/PT_GALP_12_BUT.png
2025-06-18 10:21:15,390 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/PT_CEPSA_45_PRO.png
2025-06-18 10:21:15,390 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/PT_CEPSA_35_PRO.png
2025-06-18 10:21:15,390 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/PT_CEPSA_11_PRO.png
2025-06-18 10:21:15,391 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/PT_CEPSA_12-5_BUT.png
2025-06-18 10:21:15,391 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/PT_REPSOL_45_PRO.png
2025-06-18 10:21:15,391 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/PT_REPSOL_35_PRO.png
2025-06-18 10:21:15,391 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/PT_RUBIS_45_PRO.png
2025-06-18 10:21:15,391 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/PT_RUBIS_11_PRO.png
2025-06-18 10:21:15,391 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/PT_RUBIS_13_BUT.png
2025-06-18 10:21:15,391 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/PT_RUBIS_L12_BUT.png
2025-06-18 10:21:15,392 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/ES_REPSOL_L12_BUT.jpeg
2025-06-18 10:21:15,392 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/ES_REPSOL_35_PRO.jpeg
2025-06-18 10:21:15,392 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/ES_REPSOL_11_PRO.jpeg
2025-06-18 10:21:15,392 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/ES_REPSOL_12-5_BUT.jpeg
2025-06-18 10:21:15,392 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/PT_REPSOL_45_PRO.png
2025-06-18 10:21:15,392 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/PT_REPSOL_35_PRO.png
2025-06-18 10:21:15,392 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/PT_REPSOL_11_PRO.png
2025-06-18 10:21:15,392 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/PT_REPSOL_13_BUT.png
2025-06-18 10:21:15,392 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/PT_REPSOL_12-5_BUT.png
2025-06-18 10:21:15,392 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/PT_REPSOL_K11_BUT.png
2025-06-18 10:21:15,392 - app.main - INFO - Found photo URL: /workspaces/Scrapping-Rubis/media/ES_REPSOL_L12_BUT.jpeg
2025-06-18 10:21:15,392 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/PT_RUBIS_45_PRO.png
2025-06-18 10:21:15,393 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/PT_RUBIS_11_PRO.png
2025-06-18 10:21:15,393 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/PT_RUBIS_13_BUT.png
2025-06-18 10:21:15,394 - app.main - INFO - Attempting to load image from: static/media/PT_REPSOL_11_PRO.png
2025-06-18 10:21:15,406 - app.main - INFO - Successfully inserted image for row 1
2025-06-18 10:21:15,406 - app.main - INFO - Attempting to load image from: static/media/PT_REPSOL_13_BUT.png
2025-06-18 10:21:15,407 - app.main - INFO - Successfully inserted image for row 2
2025-06-18 10:21:15,407 - app.main - INFO - Attempting to load image from: static/media/PT_REPSOL_12-5_BUT.png
2025-06-18 10:21:15,408 - app.main - INFO - Successfully inserted image for row 3
2025-06-18 10:21:15,408 - app.main - INFO - Attempting to load image from: static/media/PT_REPSOL_K11_BUT.png
2025-06-18 10:21:15,409 - app.main - INFO - Successfully inserted image for row 4
2025-06-18 10:21:15,409 - app.main - INFO - Attempting to load image from: static/media/PT_RUBIS_45_PRO.png
2025-06-18 10:21:15,412 - app.main - INFO - Successfully inserted image for row 5
2025-06-18 10:21:15,413 - app.main - INFO - Attempting to load image from: static/media/PT_RUBIS_11_PRO.png
2025-06-18 10:21:15,417 - app.main - INFO - Successfully inserted image for row 6
2025-06-18 10:21:15,417 - app.main - INFO - Attempting to load image from: static/media/PT_RUBIS_13_BUT.png
2025-06-18 10:21:15,419 - app.main - INFO - Successfully inserted image for row 7
2025-06-18 10:21:15,420 - app.main - INFO - Attempting to load image from: static/media/PT_RUBIS_L12_BUT.png
2025-06-18 10:21:15,422 - app.main - INFO - Successfully inserted image for row 8
2025-06-18 10:21:15,422 - app.main - INFO - Attempting to load image from: static/media/IT_BUTANGAS_25_PRO.png
2025-06-18 10:21:15,471 - app.main - INFO - Successfully inserted image for row 9
2025-06-18 10:21:15,471 - app.main - INFO - Attempting to load image from: static/media/IT_BUTANGAS_20_PRO.png
2025-06-18 10:21:15,522 - app.main - INFO - Successfully inserted image for row 10
2025-06-18 10:21:15,523 - app.main - INFO - Attempting to load image from: static/media/IT_BUTANGAS_15_PRO.png
2025-06-18 10:21:15,559 - app.main - INFO - Successfully inserted image for row 11
2025-06-18 10:21:15,560 - app.main - INFO - Attempting to load image from: static/media/IT_BUTANGAS_10_PRO.png
2025-06-18 10:21:15,589 - app.main - INFO - Successfully inserted image for row 12
2025-06-18 10:21:15,589 - app.main - INFO - Attempting to load image from: static/media/IT_LIQUIGAS_25_PRO.jpg
2025-06-18 10:21:15,593 - app.main - INFO - Successfully inserted image for row 13
2025-06-18 10:21:15,593 - app.main - INFO - Attempting to load image from: static/media/IT_LIQUIGAS_20_PRO.jpg
2025-06-18 10:21:15,598 - app.main - INFO - Successfully inserted image for row 14
2025-06-18 10:21:15,599 - app.main - INFO - Attempting to load image from: static/media/IT_LIQUIGAS_15_PRO.jpg
2025-06-18 10:21:15,603 - app.main - INFO - Successfully inserted image for row 15
2025-06-18 10:21:15,604 - app.main - INFO - Attempting to load image from: static/media/IT_LIQUIGAS_10_PRO.jpg
2025-06-18 10:21:15,607 - app.main - INFO - Successfully inserted image for row 16
2025-06-18 10:21:15,607 - app.main - INFO - Attempting to load image from: static/media/IT_ENI_25_PRO.jpeg
2025-06-18 10:21:15,614 - app.main - INFO - Successfully inserted image for row 17
2025-06-18 10:21:15,614 - app.main - INFO - Attempting to load image from: static/media/IT_ENI_15_PRO.jpeg
2025-06-18 10:21:15,619 - app.main - INFO - Successfully inserted image for row 18
2025-06-18 10:21:15,619 - app.main - INFO - Attempting to load image from: static/media/IT_ENI_10_PRO.jpeg
2025-06-18 10:21:15,623 - app.main - INFO - Successfully inserted image for row 19
2025-06-18 10:21:15,623 - app.main - INFO - Attempting to load image from: static/media/DE_ALUGAS_11_PRO.jpeg
2025-06-18 10:21:15,626 - app.main - INFO - Successfully inserted image for row 20
2025-06-18 10:21:15,626 - app.main - INFO - Attempting to load image from: static/media/DE_GLOBALGAS_33_PRO.jpeg
2025-06-18 10:21:15,629 - app.main - INFO - Successfully inserted image for row 21
2025-06-18 10:21:15,629 - app.main - INFO - Attempting to load image from: static/media/DE_GLOBALGAS_11_PRO.jpeg
2025-06-18 10:21:15,633 - app.main - INFO - Successfully inserted image for row 22
2025-06-18 10:21:15,633 - app.main - INFO - Attempting to load image from: static/media/DE_HOYER_33_PRO.jpeg
2025-06-18 10:21:15,635 - app.main - INFO - Successfully inserted image for row 23
2025-06-18 10:21:15,636 - app.main - INFO - Attempting to load image from: static/media/DE_HOYER_11_PRO.jpeg
2025-06-18 10:21:15,638 - app.main - INFO - Successfully inserted image for row 24
2025-06-18 10:21:15,638 - app.main - INFO - Attempting to load image from: static/media/DE_WESTFALENGAS_33_PRO.jpeg
2025-06-18 10:21:15,640 - app.main - INFO - Successfully inserted image for row 25
2025-06-18 10:21:15,641 - app.main - INFO - Attempting to load image from: static/media/DE_WESTFALENGAS_11_PRO.jpg
2025-06-18 10:21:15,642 - app.main - INFO - Successfully inserted image for row 26
2025-06-18 10:21:15,642 - app.main - INFO - Attempting to load image from: static/media/DE_TYCKZA_33_PRO.jpg
2025-06-18 10:21:15,644 - app.main - INFO - Successfully inserted image for row 27
2025-06-18 10:21:15,645 - app.main - INFO - Attempting to load image from: static/media/DE_TYCKZA_11_PRO.jpeg
2025-06-18 10:21:15,649 - app.main - INFO - Successfully inserted image for row 28
2025-06-18 10:21:15,649 - app.main - INFO - Attempting to load image from: static/media/FR_VITOGAZ_35_PRO.png
2025-06-18 10:21:15,651 - app.main - INFO - Successfully inserted image for row 29
2025-06-18 10:21:15,651 - app.main - INFO - Attempting to load image from: static/media/FR_VITOGAZ_13_PRO.png
2025-06-18 10:21:15,654 - app.main - INFO - Successfully inserted image for row 30
2025-06-18 10:21:15,654 - app.main - INFO - Attempting to load image from: static/media/FR_VITOGAZ_13_BUT.png
2025-06-18 10:21:15,658 - app.main - INFO - Successfully inserted image for row 31
2025-06-18 10:21:15,658 - app.main - INFO - Attempting to load image from: static/media/FR_UNERGIES_13_PRO.png
2025-06-18 10:21:15,662 - app.main - INFO - Successfully inserted image for row 32
2025-06-18 10:21:15,663 - app.main - INFO - Attempting to load image from: static/media/FR_UNERGIES_13_BUT.png
2025-06-18 10:21:15,666 - app.main - INFO - Successfully inserted image for row 33
2025-06-18 10:21:15,666 - app.main - INFO - Attempting to load image from: static/media/FR_PRIMAGAZ_35_PRO.jpeg
2025-06-18 10:21:15,668 - app.main - INFO - Successfully inserted image for row 34
2025-06-18 10:21:15,669 - app.main - INFO - Attempting to load image from: static/media/FR_PRIMAGAZ_13_PRO.jpeg
2025-06-18 10:21:15,671 - app.main - INFO - Successfully inserted image for row 35
2025-06-18 10:21:15,671 - app.main - INFO - Attempting to load image from: static/media/FR_PRIMAGAZ_13_BUT.jpeg
2025-06-18 10:21:15,673 - app.main - INFO - Successfully inserted image for row 36
2025-06-18 10:21:15,673 - app.main - INFO - Attempting to load image from: static/media/FR_EHO_35_PRO.png
2025-06-18 10:21:15,676 - app.main - INFO - Successfully inserted image for row 37
2025-06-18 10:21:15,676 - app.main - INFO - Attempting to load image from: static/media/FR_EHO_13_PRO.png
2025-06-18 10:21:15,681 - app.main - INFO - Successfully inserted image for row 38
2025-06-18 10:21:15,681 - app.main - INFO - Attempting to load image from: static/media/FR_EHO_12-5_BUT.png
2025-06-18 10:21:15,685 - app.main - INFO - Successfully inserted image for row 39
2025-06-18 10:21:15,685 - app.main - INFO - Attempting to load image from: static/media/FR_CLAIRGAZ_11_PRO.jpg
2025-06-18 10:21:15,687 - app.main - INFO - Successfully inserted image for row 40
2025-06-18 10:21:15,687 - app.main - INFO - Attempting to load image from: static/media/FR_CLAIRGAZ_13_BUT.jpg
2025-06-18 10:21:15,695 - app.main - INFO - Successfully inserted image for row 41
2025-06-18 10:21:15,695 - app.main - INFO - Attempting to load image from: static/media/FR_CARREFOUR_13_BUT.jpg
2025-06-18 10:21:15,754 - app.main - INFO - Successfully inserted image for row 42
2025-06-18 10:21:15,754 - app.main - INFO - Attempting to load image from: static/media/FR_BUTAGAZ_35_PRO.png
2025-06-18 10:21:15,757 - app.main - INFO - Successfully inserted image for row 43
2025-06-18 10:21:15,757 - app.main - INFO - Attempting to load image from: static/media/FR_BUTAGAZ_13_PRO.png
2025-06-18 10:21:15,761 - app.main - INFO - Successfully inserted image for row 44
2025-06-18 10:21:15,761 - app.main - INFO - Attempting to load image from: static/media/FR_BUTAGAZ_L10_BUT.png
2025-06-18 10:21:15,767 - app.main - INFO - Successfully inserted image for row 45
2025-06-18 10:21:15,767 - app.main - INFO - Attempting to load image from: static/media/FR_BUTAGAZ_13_BUT.png
2025-06-18 10:21:15,771 - app.main - INFO - Successfully inserted image for row 46
2025-06-18 10:21:15,771 - app.main - INFO - Attempting to load image from: static/media/FR_AUCHAN_13_PRO.png
2025-06-18 10:21:15,774 - app.main - INFO - Successfully inserted image for row 47
2025-06-18 10:21:15,774 - app.main - INFO - Attempting to load image from: static/media/FR_AUCHAN_13_BUT.png
2025-06-18 10:21:15,778 - app.main - INFO - Successfully inserted image for row 48
2025-06-18 10:21:15,778 - app.main - INFO - Attempting to load image from: static/media/FR_ANTARGAZ_35_PRO.png
2025-06-18 10:21:15,783 - app.main - INFO - Successfully inserted image for row 49
2025-06-18 10:21:15,783 - app.main - INFO - Attempting to load image from: static/media/FR_ANTARGAZ_13_PRO.png
2025-06-18 10:21:15,788 - app.main - INFO - Successfully inserted image for row 50
2025-06-18 10:21:15,788 - app.main - INFO - Attempting to load image from: static/media/FR_ANTARGAZ_L10_BUT.png
2025-06-18 10:21:15,792 - app.main - INFO - Successfully inserted image for row 51
2025-06-18 10:21:15,792 - app.main - INFO - Attempting to load image from: static/media/FR_ANTARGAZ_13_BUT.png
2025-06-18 10:21:15,796 - app.main - INFO - Successfully inserted image for row 52
2025-06-18 10:21:15,797 - app.main - INFO - Attempting to load image from: static/media/UK_FLOGAS_47_PRO.jpeg
2025-06-18 10:21:15,799 - app.main - INFO - Successfully inserted image for row 53
2025-06-18 10:21:15,799 - app.main - INFO - Attempting to load image from: static/media/UK_FLOGAS_19_PRO.jpeg
2025-06-18 10:21:15,801 - app.main - INFO - Successfully inserted image for row 54
2025-06-18 10:21:15,802 - app.main - INFO - Attempting to load image from: static/media/UK_FLOGAS_13(21mm)_BUT.jpg
2025-06-18 10:21:15,804 - app.main - INFO - Successfully inserted image for row 55
2025-06-18 10:21:15,804 - app.main - INFO - Attempting to load image from: static/media/UK_FLOGAS_13(20mm)_BUT.jpg
2025-06-18 10:21:15,806 - app.main - INFO - Successfully inserted image for row 56
2025-06-18 10:21:15,806 - app.main - INFO - Attempting to load image from: static/media/UK_FLOGAS_11_PRO.jpeg
2025-06-18 10:21:15,808 - app.main - INFO - Successfully inserted image for row 57
2025-06-18 10:21:15,808 - app.main - INFO - Attempting to load image from: static/media/UK_FLOGAS_L10_PRO.png
2025-06-18 10:21:15,813 - app.main - INFO - Successfully inserted image for row 58
2025-06-18 10:21:15,813 - app.main - INFO - Attempting to load image from: static/media/UK_EXTRAGAS_46_PRO.png
2025-06-18 10:21:15,818 - app.main - INFO - Successfully inserted image for row 59
2025-06-18 10:21:15,818 - app.main - INFO - Attempting to load image from: static/media/UK_EXTRAGAS_19_PRO.png
2025-06-18 10:21:15,823 - app.main - INFO - Successfully inserted image for row 60
2025-06-18 10:21:15,823 - app.main - INFO - Attempting to load image from: static/media/UK_EXTRAGAS_11_PRO.png
2025-06-18 10:21:15,827 - app.main - INFO - Successfully inserted image for row 61
2025-06-18 10:21:15,827 - app.main - INFO - Attempting to load image from: static/media/UK_EXTRAGAS_12_BUT.png
2025-06-18 10:21:15,833 - app.main - INFO - Successfully inserted image for row 62
2025-06-18 10:21:15,833 - app.main - INFO - Attempting to load image from: static/media/UK_CALOR_47_PRO.jpeg
2025-06-18 10:21:15,836 - app.main - INFO - Successfully inserted image for row 63
2025-06-18 10:21:15,836 - app.main - INFO - Attempting to load image from: static/media/UK_CALOR_19_PRO.jpeg
2025-06-18 10:21:15,838 - app.main - INFO - Successfully inserted image for row 64
2025-06-18 10:21:15,838 - app.main - INFO - Attempting to load image from: static/media/UK_CALOR_13_PRO.jpeg
2025-06-18 10:21:15,841 - app.main - INFO - Successfully inserted image for row 65
2025-06-18 10:21:15,841 - app.main - INFO - Attempting to load image from: static/media/UK_CALOR_15_BUT.jpeg
2025-06-18 10:21:15,845 - app.main - INFO - Successfully inserted image for row 66
2025-06-18 10:21:15,845 - app.main - INFO - Attempting to load image from: static/media/CH_VITOGAZ_L10_PRO.jpeg
2025-06-18 10:21:15,849 - app.main - INFO - Successfully inserted image for row 67
2025-06-18 10:21:15,850 - app.main - INFO - Attempting to load image from: static/media/ES_REPSOL_11_PRO.jpeg
2025-06-18 10:21:15,852 - app.main - INFO - Successfully inserted image for row 68
2025-06-18 10:21:15,852 - app.main - INFO - Attempting to load image from: static/media/ES_REPSOL_12-5_BUT.jpeg
2025-06-18 10:21:15,854 - app.main - INFO - Successfully inserted image for row 69
2025-06-18 10:21:15,854 - app.main - INFO - Attempting to load image from: static/media/PT_GALP_10_PRO.png
2025-06-18 10:21:15,858 - app.main - INFO - Successfully inserted image for row 70
2025-06-18 10:21:15,858 - app.main - INFO - Attempting to load image from: static/media/CH_FLAGA_35_PRO.jpeg
2025-06-18 10:21:15,860 - app.main - INFO - Successfully inserted image for row 71
2025-06-18 10:21:15,860 - app.main - INFO - Attempting to load image from: static/media/CH_FLAGA_L10_PRO.jpeg
2025-06-18 10:21:15,863 - app.main - INFO - Successfully inserted image for row 72
2025-06-18 10:21:15,864 - app.main - INFO - Attempting to load image from: static/media/CH_FLAGA_10-5_PRO.jpeg
2025-06-18 10:21:15,867 - app.main - INFO - Successfully inserted image for row 73
2025-06-18 10:21:15,867 - app.main - INFO - Attempting to load image from: static/media/CH_GLOBALGAS_33_PRO.jpeg
2025-06-18 10:21:15,870 - app.main - INFO - Successfully inserted image for row 74
2025-06-18 10:21:15,871 - app.main - INFO - Attempting to load image from: static/media/CH_GLOBALGAS_11_PRO.jpeg
2025-06-18 10:21:15,876 - app.main - INFO - Successfully inserted image for row 75
2025-06-18 10:21:15,876 - app.main - INFO - Attempting to load image from: static/media/CH_VITOGAZ_35_PRO.jpeg
2025-06-18 10:21:15,882 - app.main - INFO - Successfully inserted image for row 76
2025-06-18 10:21:15,883 - app.main - INFO - Attempting to load image from: static/media/CH_VITOGAZ_10-5_PRO.jpeg
2025-06-18 10:21:15,888 - app.main - INFO - Successfully inserted image for row 77
2025-06-18 10:21:15,888 - app.main - INFO - Attempting to load image from: static/media/ES_REPSOL_L12_BUT.jpeg
2025-06-18 10:21:15,892 - app.main - INFO - Successfully inserted image for row 78
2025-06-18 10:21:15,893 - app.main - INFO - Attempting to load image from: static/media/ES_REPSOL_35_PRO.jpeg
2025-06-18 10:21:15,897 - app.main - INFO - Successfully inserted image for row 79
2025-06-18 10:21:15,897 - app.main - INFO - Attempting to load image from: static/media/ES_CEPSA_11_PRO.jpeg
2025-06-18 10:21:15,899 - app.main - INFO - Successfully inserted image for row 80
2025-06-18 10:21:15,899 - app.main - INFO - Attempting to load image from: static/media/ES_CEPSA_L11_PRO.jpeg
2025-06-18 10:21:15,900 - app.main - INFO - Successfully inserted image for row 81
2025-06-18 10:21:15,900 - app.main - INFO - Attempting to load image from: static/media/ES_CEPSA_L12-5_BUT.jpeg
2025-06-18 10:21:15,902 - app.main - INFO - Successfully inserted image for row 82
2025-06-18 10:21:15,902 - app.main - INFO - Attempting to load image from: static/media/ES_GALP_35_PRO.png
2025-06-18 10:21:15,903 - app.main - INFO - Successfully inserted image for row 83
2025-06-18 10:21:15,904 - app.main - INFO - Attempting to load image from: static/media/ES_GALP_11_PRO.png
2025-06-18 10:21:15,905 - app.main - INFO - Successfully inserted image for row 84
2025-06-18 10:21:15,905 - app.main - INFO - Attempting to load image from: static/media/ES_GALP_P12_BUT.png
2025-06-18 10:21:15,909 - app.main - INFO - Successfully inserted image for row 85
2025-06-18 10:21:15,909 - app.main - INFO - Attempting to load image from: static/media/ES_GALP_12.5_BUT.png
2025-06-18 10:21:15,910 - app.main - INFO - Successfully inserted image for row 86
2025-06-18 10:21:15,911 - app.main - INFO - Attempting to load image from: static/media/PT_TUTIGAS_45_PRO.jpeg
2025-06-18 10:21:15,916 - app.main - INFO - Successfully inserted image for row 87
2025-06-18 10:21:15,916 - app.main - INFO - Attempting to load image from: static/media/PT_TUTIGAS_11_PRO.jpeg
2025-06-18 10:21:15,919 - app.main - INFO - Successfully inserted image for row 88
2025-06-18 10:21:15,919 - app.main - INFO - Attempting to load image from: static/media/PT_TUTIGAS_13_BUT.jpeg
2025-06-18 10:21:15,922 - app.main - INFO - Successfully inserted image for row 89
2025-06-18 10:21:15,922 - app.main - INFO - Attempting to load image from: static/media/PT_TUTIGAS_XL12-5_BUT.png
2025-06-18 10:21:15,926 - app.main - INFO - Successfully inserted image for row 90
2025-06-18 10:21:15,926 - app.main - INFO - Attempting to load image from: static/media/PT_PRIO_45_PRO.jpeg
2025-06-18 10:21:15,933 - app.main - INFO - Successfully inserted image for row 91
2025-06-18 10:21:15,933 - app.main - INFO - Attempting to load image from: static/media/PT_PRIO_9_BUT.jpeg
2025-06-18 10:21:15,937 - app.main - INFO - Successfully inserted image for row 92
2025-06-18 10:21:15,937 - app.main - INFO - Attempting to load image from: static/media/PT_OZ_45_PRO.png
2025-06-18 10:21:15,941 - app.main - INFO - Successfully inserted image for row 93
2025-06-18 10:21:15,941 - app.main - INFO - Attempting to load image from: static/media/PT_OZ_11_PRO.png
2025-06-18 10:21:15,948 - app.main - INFO - Successfully inserted image for row 94
2025-06-18 10:21:15,948 - app.main - INFO - Attempting to load image from: static/media/PT_OZ_13_BUT.png
2025-06-18 10:21:15,953 - app.main - INFO - Successfully inserted image for row 95
2025-06-18 10:21:15,953 - app.main - INFO - Attempting to load image from: static/media/PT_OZ_P13_BUT.png
2025-06-18 10:21:15,958 - app.main - INFO - Successfully inserted image for row 96
2025-06-18 10:21:15,959 - app.main - INFO - Attempting to load image from: static/media/PT_GALP_45_PRO.png
2025-06-18 10:21:15,960 - app.main - INFO - Successfully inserted image for row 97
2025-06-18 10:21:15,960 - app.main - INFO - Attempting to load image from: static/media/PT_GALP_11_PRO.png
2025-06-18 10:21:15,963 - app.main - INFO - Successfully inserted image for row 98
2025-06-18 10:21:15,963 - app.main - INFO - Attempting to load image from: static/media/PT_GALP_13_BUT.png
2025-06-18 10:21:15,965 - app.main - INFO - Successfully inserted image for row 99
2025-06-18 10:21:15,966 - app.main - INFO - Attempting to load image from: static/media/PT_GALP_12_BUT.png
2025-06-18 10:21:15,969 - app.main - INFO - Successfully inserted image for row 100
2025-06-18 10:21:15,969 - app.main - INFO - Attempting to load image from: static/media/PT_CEPSA_45_PRO.png
2025-06-18 10:21:15,973 - app.main - INFO - Successfully inserted image for row 101
2025-06-18 10:21:15,973 - app.main - INFO - Attempting to load image from: static/media/PT_CEPSA_35_PRO.png
2025-06-18 10:21:15,976 - app.main - INFO - Successfully inserted image for row 102
2025-06-18 10:21:15,976 - app.main - INFO - Attempting to load image from: static/media/PT_CEPSA_11_PRO.png
2025-06-18 10:21:15,981 - app.main - INFO - Successfully inserted image for row 103
2025-06-18 10:21:15,981 - app.main - INFO - Attempting to load image from: static/media/PT_CEPSA_12-5_BUT.png
2025-06-18 10:21:15,985 - app.main - INFO - Successfully inserted image for row 104
2025-06-18 10:21:15,985 - app.main - INFO - Attempting to load image from: static/media/PT_REPSOL_45_PRO.png
2025-06-18 10:21:15,986 - app.main - INFO - Successfully inserted image for row 105
2025-06-18 10:21:15,987 - app.main - INFO - Attempting to load image from: static/media/PT_REPSOL_35_PRO.png
2025-06-18 10:21:15,988 - app.main - INFO - Successfully inserted image for row 106
2025-06-18 10:21:15,988 - app.main - INFO - Attempting to load image from: static/media/PT_RUBIS_45_PRO.png
2025-06-18 10:21:15,990 - app.main - INFO - Successfully inserted image for row 107
2025-06-18 10:21:15,991 - app.main - INFO - Attempting to load image from: static/media/PT_RUBIS_11_PRO.png
2025-06-18 10:21:15,994 - app.main - INFO - Successfully inserted image for row 108
2025-06-18 10:21:15,994 - app.main - INFO - Attempting to load image from: static/media/PT_RUBIS_13_BUT.png
2025-06-18 10:21:15,998 - app.main - INFO - Successfully inserted image for row 109
2025-06-18 10:21:15,998 - app.main - INFO - Attempting to load image from: static/media/PT_RUBIS_L12_BUT.png
2025-06-18 10:21:16,001 - app.main - INFO - Successfully inserted image for row 110
2025-06-18 10:21:16,001 - app.main - INFO - Attempting to load image from: static/media/ES_REPSOL_35_PRO.jpeg
2025-06-18 10:21:16,003 - app.main - INFO - Successfully inserted image for row 111
2025-06-18 10:21:16,004 - app.main - INFO - Attempting to load image from: static/media/ES_REPSOL_11_PRO.jpeg
2025-06-18 10:21:16,006 - app.main - INFO - Successfully inserted image for row 112
2025-06-18 10:21:16,006 - app.main - INFO - Attempting to load image from: static/media/ES_REPSOL_12-5_BUT.jpeg
2025-06-18 10:21:16,009 - app.main - INFO - Successfully inserted image for row 113
2025-06-18 10:21:16,009 - app.main - INFO - Attempting to load image from: static/media/PT_REPSOL_45_PRO.png
2025-06-18 10:21:16,010 - app.main - INFO - Successfully inserted image for row 114
2025-06-18 10:21:16,010 - app.main - INFO - Attempting to load image from: static/media/PT_REPSOL_35_PRO.png
2025-06-18 10:21:16,012 - app.main - INFO - Successfully inserted image for row 115
2025-06-18 10:21:16,012 - app.main - INFO - Attempting to load image from: static/media/PT_REPSOL_11_PRO.png
2025-06-18 10:21:16,014 - app.main - INFO - Successfully inserted image for row 116
2025-06-18 10:21:16,014 - app.main - INFO - Attempting to load image from: static/media/PT_REPSOL_13_BUT.png
2025-06-18 10:21:16,015 - app.main - INFO - Successfully inserted image for row 117
2025-06-18 10:21:16,016 - app.main - INFO - Attempting to load image from: static/media/PT_REPSOL_12-5_BUT.png
2025-06-18 10:21:16,017 - app.main - INFO - Successfully inserted image for row 118
2025-06-18 10:21:16,017 - app.main - INFO - Attempting to load image from: static/media/PT_REPSOL_K11_BUT.png
2025-06-18 10:21:16,018 - app.main - INFO - Successfully inserted image for row 119
2025-06-18 10:21:16,018 - app.main - INFO - Attempting to load image from: static/media/PT_RUBIS_45_PRO.png
2025-06-18 10:21:16,020 - app.main - INFO - Successfully inserted image for row 120
2025-06-18 10:21:16,021 - app.main - INFO - Attempting to load image from: static/media/PT_RUBIS_11_PRO.png
2025-06-18 10:21:16,023 - app.main - INFO - Successfully inserted image for row 121
2025-06-18 10:21:16,024 - app.main - INFO - Attempting to load image from: static/media/PT_RUBIS_13_BUT.png
2025-06-18 10:21:16,027 - app.main - INFO - Successfully inserted image for row 122
2025-06-18 10:21:20,065 - app.services.email_service - INFO - Email template directory: C:\Users\<USER>\Downloads\Rubis-Bottle-Data-codespace-laughing-happiness-vxr446x59xx3x4pq\Rubis-Bottle-Data-codespace-laughing-happiness-vxr446x59xx3x4pq\templates\email
2025-06-18 10:21:24,754 - app.services.email_service - INFO - Email <NAME_EMAIL> with subject: Gas Prices Report - Generated
2025-06-18 10:21:24,789 - app.main - INFO - Report generation email sent successfully
2025-06-18 10:21:24,792 - app.middleware.performance - WARNING - Slow request: GET /generate-report took 9.502s from testclient
2025-06-18 10:21:24,797 - httpx - INFO - HTTP Request: GET http://testserver/generate-report "HTTP/1.1 200 OK"
2025-06-18 10:21:24,834 - app.services.email_service - INFO - Email template directory: C:\Users\<USER>\Downloads\Rubis-Bottle-Data-codespace-laughing-happiness-vxr446x59xx3x4pq\Rubis-Bottle-Data-codespace-laughing-happiness-vxr446x59xx3x4pq\templates\email
2025-06-18 10:21:24,843 - app.services.email_service - INFO - Successfully rendered template: report_template.html
2025-06-18 10:21:26,477 - app.services.email_service - INFO - Email <NAME_EMAIL> with subject: Gas Price Report - 2025-06-18
2025-06-18 10:21:26,512 - app.services.email_service - INFO - Report email sent successfully with 1 attachments
2025-06-18 10:21:26,515 - httpx - INFO - HTTP Request: GET http://testserver/generate-email-report "HTTP/1.1 200 OK"
2025-06-18 10:22:48,861 - app.services.email_service - INFO - Logging initialized
2025-06-18 10:22:49,017 - httpx - INFO - HTTP Request: GET http://testserver/database "HTTP/1.1 200 OK"
2025-06-18 10:22:49,029 - httpx - INFO - HTTP Request: GET http://testserver/manual-request "HTTP/1.1 200 OK"
2025-06-18 10:22:49,122 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/PT_REPSOL_11_PRO.png
2025-06-18 10:22:49,122 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/PT_REPSOL_13_BUT.png
2025-06-18 10:22:49,122 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/PT_REPSOL_12-5_BUT.png
2025-06-18 10:22:49,122 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/PT_REPSOL_K11_BUT.png
2025-06-18 10:22:49,122 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/PT_RUBIS_45_PRO.png
2025-06-18 10:22:49,123 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/PT_RUBIS_11_PRO.png
2025-06-18 10:22:49,123 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/PT_RUBIS_13_BUT.png
2025-06-18 10:22:49,123 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/PT_RUBIS_L12_BUT.png
2025-06-18 10:22:49,123 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/IT_BUTANGAS_25_PRO.png
2025-06-18 10:22:49,123 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/IT_BUTANGAS_20_PRO.png
2025-06-18 10:22:49,123 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/IT_BUTANGAS_15_PRO.png
2025-06-18 10:22:49,123 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/IT_BUTANGAS_10_PRO.png
2025-06-18 10:22:49,123 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/IT_LIQUIGAS_25_PRO.jpg
2025-06-18 10:22:49,123 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/IT_LIQUIGAS_20_PRO.jpg
2025-06-18 10:22:49,123 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/IT_LIQUIGAS_15_PRO.jpg
2025-06-18 10:22:49,123 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/IT_LIQUIGAS_10_PRO.jpg
2025-06-18 10:22:49,124 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/IT_ENI_25_PRO.jpeg
2025-06-18 10:22:49,124 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/IT_ENI_15_PRO.jpeg
2025-06-18 10:22:49,124 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/IT_ENI_10_PRO.jpeg
2025-06-18 10:22:49,124 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/DE_ALUGAS_11_PRO.jpeg
2025-06-18 10:22:49,124 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/DE_GLOBALGAS_33_PRO.jpeg
2025-06-18 10:22:49,124 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/DE_GLOBALGAS_11_PRO.jpeg
2025-06-18 10:22:49,124 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/DE_HOYER_33_PRO.jpeg
2025-06-18 10:22:49,124 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/DE_HOYER_11_PRO.jpeg
2025-06-18 10:22:49,124 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/DE_WESTFALENGAS_33_PRO.jpeg
2025-06-18 10:22:49,124 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/DE_WESTFALENGAS_11_PRO.jpg
2025-06-18 10:22:49,124 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/DE_TYCKZA_33_PRO.jpg
2025-06-18 10:22:49,124 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/DE_TYCKZA_11_PRO.jpeg
2025-06-18 10:22:49,124 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/FR_VITOGAZ_35_PRO.png
2025-06-18 10:22:49,124 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/FR_VITOGAZ_13_PRO.png
2025-06-18 10:22:49,124 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/FR_VITOGAZ_13_BUT.png
2025-06-18 10:22:49,125 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/FR_UNERGIES_13_PRO.png
2025-06-18 10:22:49,125 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/FR_UNERGIES_13_BUT.png
2025-06-18 10:22:49,125 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/FR_PRIMAGAZ_35_PRO.jpeg
2025-06-18 10:22:49,125 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/FR_PRIMAGAZ_13_PRO.jpeg
2025-06-18 10:22:49,125 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/FR_PRIMAGAZ_13_BUT.jpeg
2025-06-18 10:22:49,125 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/FR_EHO_35_PRO.png
2025-06-18 10:22:49,125 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/FR_EHO_13_PRO.png
2025-06-18 10:22:49,125 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/FR_EHO_12-5_BUT.png
2025-06-18 10:22:49,125 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/FR_CLAIRGAZ_11_PRO.jpg
2025-06-18 10:22:49,125 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/FR_CLAIRGAZ_13_BUT.jpg
2025-06-18 10:22:49,125 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/FR_CARREFOUR_13_BUT.jpg
2025-06-18 10:22:49,125 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/FR_BUTAGAZ_35_PRO.png
2025-06-18 10:22:49,125 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/FR_BUTAGAZ_13_PRO.png
2025-06-18 10:22:49,125 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/FR_BUTAGAZ_L10_BUT.png
2025-06-18 10:22:49,126 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/FR_BUTAGAZ_13_BUT.png
2025-06-18 10:22:49,126 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/FR_AUCHAN_13_PRO.png
2025-06-18 10:22:49,126 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/FR_AUCHAN_13_BUT.png
2025-06-18 10:22:49,126 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/FR_ANTARGAZ_35_PRO.png
2025-06-18 10:22:49,126 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/FR_ANTARGAZ_13_PRO.png
2025-06-18 10:22:49,126 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/FR_ANTARGAZ_L10_BUT.png
2025-06-18 10:22:49,126 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/FR_ANTARGAZ_13_BUT.png
2025-06-18 10:22:49,126 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/UK_FLOGAS_47_PRO.jpeg
2025-06-18 10:22:49,126 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/UK_FLOGAS_19_PRO.jpeg
2025-06-18 10:22:49,126 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/UK_FLOGAS_13(21mm)_BUT.jpg
2025-06-18 10:22:49,126 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/UK_FLOGAS_13(20mm)_BUT.jpg
2025-06-18 10:22:49,127 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/UK_FLOGAS_11_PRO.jpeg
2025-06-18 10:22:49,127 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/UK_FLOGAS_L10_PRO.png
2025-06-18 10:22:49,127 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/UK_EXTRAGAS_46_PRO.png
2025-06-18 10:22:49,127 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/UK_EXTRAGAS_19_PRO.png
2025-06-18 10:22:49,127 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/UK_EXTRAGAS_11_PRO.png
2025-06-18 10:22:49,127 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/UK_EXTRAGAS_12_BUT.png
2025-06-18 10:22:49,127 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/UK_CALOR_47_PRO.jpeg
2025-06-18 10:22:49,127 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/UK_CALOR_19_PRO.jpeg
2025-06-18 10:22:49,127 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/UK_CALOR_13_PRO.jpeg
2025-06-18 10:22:49,127 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/UK_CALOR_15_BUT.jpeg
2025-06-18 10:22:49,127 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/CH_FLAGA_35_PRO.jpeg
2025-06-18 10:22:49,127 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/CH_FLAGA_L10_PRO.jpeg
2025-06-18 10:22:49,127 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/CH_FLAGA_10-5_PRO.jpeg
2025-06-18 10:22:49,127 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/CH_GLOBALGAS_33_PRO.jpeg
2025-06-18 10:22:49,127 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/CH_GLOBALGAS_11_PRO.jpeg
2025-06-18 10:22:49,127 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/CH_VITOGAZ_35_PRO.jpeg
2025-06-18 10:22:49,128 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/CH_VITOGAZ_10-5_PRO.jpeg
2025-06-18 10:22:49,128 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/ES_REPSOL_L12_BUT.jpeg
2025-06-18 10:22:49,128 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/ES_REPSOL_35_PRO.jpeg
2025-06-18 10:22:49,128 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/ES_CEPSA_11_PRO.jpeg
2025-06-18 10:22:49,128 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/ES_CEPSA_L11_PRO.jpeg
2025-06-18 10:22:49,128 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/ES_CEPSA_L12-5_BUT.jpeg
2025-06-18 10:22:49,128 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/ES_GALP_35_PRO.png
2025-06-18 10:22:49,128 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/ES_GALP_11_PRO.png
2025-06-18 10:22:49,128 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/ES_GALP_P12_BUT.png
2025-06-18 10:22:49,128 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/ES_GALP_12.5_BUT.png
2025-06-18 10:22:49,128 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/PT_TUTIGAS_45_PRO.jpeg
2025-06-18 10:22:49,128 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/PT_TUTIGAS_11_PRO.jpeg
2025-06-18 10:22:49,128 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/PT_TUTIGAS_13_BUT.jpeg
2025-06-18 10:22:49,128 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/PT_TUTIGAS_XL12-5_BUT.png
2025-06-18 10:22:49,128 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/PT_PRIO_45_PRO.jpeg
2025-06-18 10:22:49,128 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/PT_PRIO_9_BUT.jpeg
2025-06-18 10:22:49,129 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/PT_OZ_45_PRO.png
2025-06-18 10:22:49,129 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/PT_OZ_11_PRO.png
2025-06-18 10:22:49,129 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/PT_OZ_13_BUT.png
2025-06-18 10:22:49,129 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/PT_OZ_P13_BUT.png
2025-06-18 10:22:49,129 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/PT_GALP_45_PRO.png
2025-06-18 10:22:49,129 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/PT_GALP_11_PRO.png
2025-06-18 10:22:49,129 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/PT_GALP_13_BUT.png
2025-06-18 10:22:49,129 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/PT_GALP_12_BUT.png
2025-06-18 10:22:49,129 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/PT_CEPSA_45_PRO.png
2025-06-18 10:22:49,129 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/PT_CEPSA_35_PRO.png
2025-06-18 10:22:49,129 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/PT_CEPSA_11_PRO.png
2025-06-18 10:22:49,129 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/PT_CEPSA_12-5_BUT.png
2025-06-18 10:22:49,129 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/PT_REPSOL_45_PRO.png
2025-06-18 10:22:49,129 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/PT_REPSOL_35_PRO.png
2025-06-18 10:22:49,130 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/PT_RUBIS_45_PRO.png
2025-06-18 10:22:49,130 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/PT_RUBIS_11_PRO.png
2025-06-18 10:22:49,130 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/PT_RUBIS_13_BUT.png
2025-06-18 10:22:49,130 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/PT_RUBIS_L12_BUT.png
2025-06-18 10:22:49,130 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/ES_REPSOL_L12_BUT.jpeg
2025-06-18 10:22:49,130 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/ES_REPSOL_35_PRO.jpeg
2025-06-18 10:22:49,131 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/ES_REPSOL_11_PRO.jpeg
2025-06-18 10:22:49,131 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/ES_REPSOL_12-5_BUT.jpeg
2025-06-18 10:22:49,131 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/PT_REPSOL_45_PRO.png
2025-06-18 10:22:49,131 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/PT_REPSOL_35_PRO.png
2025-06-18 10:22:49,131 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/PT_REPSOL_11_PRO.png
2025-06-18 10:22:49,131 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/PT_REPSOL_13_BUT.png
2025-06-18 10:22:49,131 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/PT_REPSOL_12-5_BUT.png
2025-06-18 10:22:49,131 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/PT_REPSOL_K11_BUT.png
2025-06-18 10:22:49,131 - app.main - INFO - Found photo URL: /workspaces/Scrapping-Rubis/media/ES_REPSOL_L12_BUT.jpeg
2025-06-18 10:22:49,131 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/PT_RUBIS_45_PRO.png
2025-06-18 10:22:49,131 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/PT_RUBIS_11_PRO.png
2025-06-18 10:22:49,132 - app.main - INFO - Found photo URL: /workspaces/codespaces-blank/static/media/PT_RUBIS_13_BUT.png
2025-06-18 10:22:49,135 - app.main - INFO - Attempting to load image from: static/media/PT_REPSOL_11_PRO.png
2025-06-18 10:22:49,146 - app.main - INFO - Successfully inserted image for row 1
2025-06-18 10:22:49,147 - app.main - INFO - Attempting to load image from: static/media/PT_REPSOL_13_BUT.png
2025-06-18 10:22:49,148 - app.main - INFO - Successfully inserted image for row 2
2025-06-18 10:22:49,148 - app.main - INFO - Attempting to load image from: static/media/PT_REPSOL_12-5_BUT.png
2025-06-18 10:22:49,149 - app.main - INFO - Successfully inserted image for row 3
2025-06-18 10:22:49,149 - app.main - INFO - Attempting to load image from: static/media/PT_REPSOL_K11_BUT.png
2025-06-18 10:22:49,150 - app.main - INFO - Successfully inserted image for row 4
2025-06-18 10:22:49,150 - app.main - INFO - Attempting to load image from: static/media/PT_RUBIS_45_PRO.png
2025-06-18 10:22:49,154 - app.main - INFO - Successfully inserted image for row 5
2025-06-18 10:22:49,155 - app.main - INFO - Attempting to load image from: static/media/PT_RUBIS_11_PRO.png
2025-06-18 10:22:49,159 - app.main - INFO - Successfully inserted image for row 6
2025-06-18 10:22:49,159 - app.main - INFO - Attempting to load image from: static/media/PT_RUBIS_13_BUT.png
2025-06-18 10:22:49,162 - app.main - INFO - Successfully inserted image for row 7
2025-06-18 10:22:49,162 - app.main - INFO - Attempting to load image from: static/media/PT_RUBIS_L12_BUT.png
2025-06-18 10:22:49,165 - app.main - INFO - Successfully inserted image for row 8
2025-06-18 10:22:49,165 - app.main - INFO - Attempting to load image from: static/media/IT_BUTANGAS_25_PRO.png
2025-06-18 10:22:49,226 - app.main - INFO - Successfully inserted image for row 9
2025-06-18 10:22:49,226 - app.main - INFO - Attempting to load image from: static/media/IT_BUTANGAS_20_PRO.png
2025-06-18 10:22:49,275 - app.main - INFO - Successfully inserted image for row 10
2025-06-18 10:22:49,276 - app.main - INFO - Attempting to load image from: static/media/IT_BUTANGAS_15_PRO.png
2025-06-18 10:22:49,309 - app.main - INFO - Successfully inserted image for row 11
2025-06-18 10:22:49,309 - app.main - INFO - Attempting to load image from: static/media/IT_BUTANGAS_10_PRO.png
2025-06-18 10:22:49,345 - app.main - INFO - Successfully inserted image for row 12
2025-06-18 10:22:49,345 - app.main - INFO - Attempting to load image from: static/media/IT_LIQUIGAS_25_PRO.jpg
2025-06-18 10:22:49,350 - app.main - INFO - Successfully inserted image for row 13
2025-06-18 10:22:49,350 - app.main - INFO - Attempting to load image from: static/media/IT_LIQUIGAS_20_PRO.jpg
2025-06-18 10:22:49,356 - app.main - INFO - Successfully inserted image for row 14
2025-06-18 10:22:49,356 - app.main - INFO - Attempting to load image from: static/media/IT_LIQUIGAS_15_PRO.jpg
2025-06-18 10:22:49,360 - app.main - INFO - Successfully inserted image for row 15
2025-06-18 10:22:49,361 - app.main - INFO - Attempting to load image from: static/media/IT_LIQUIGAS_10_PRO.jpg
2025-06-18 10:22:49,364 - app.main - INFO - Successfully inserted image for row 16
2025-06-18 10:22:49,365 - app.main - INFO - Attempting to load image from: static/media/IT_ENI_25_PRO.jpeg
2025-06-18 10:22:49,375 - app.main - INFO - Successfully inserted image for row 17
2025-06-18 10:22:49,376 - app.main - INFO - Attempting to load image from: static/media/IT_ENI_15_PRO.jpeg
2025-06-18 10:22:49,380 - app.main - INFO - Successfully inserted image for row 18
2025-06-18 10:22:49,380 - app.main - INFO - Attempting to load image from: static/media/IT_ENI_10_PRO.jpeg
2025-06-18 10:22:49,385 - app.main - INFO - Successfully inserted image for row 19
2025-06-18 10:22:49,386 - app.main - INFO - Attempting to load image from: static/media/DE_ALUGAS_11_PRO.jpeg
2025-06-18 10:22:49,391 - app.main - INFO - Successfully inserted image for row 20
2025-06-18 10:22:49,392 - app.main - INFO - Attempting to load image from: static/media/DE_GLOBALGAS_33_PRO.jpeg
2025-06-18 10:22:49,395 - app.main - INFO - Successfully inserted image for row 21
2025-06-18 10:22:49,395 - app.main - INFO - Attempting to load image from: static/media/DE_GLOBALGAS_11_PRO.jpeg
2025-06-18 10:22:49,399 - app.main - INFO - Successfully inserted image for row 22
2025-06-18 10:22:49,399 - app.main - INFO - Attempting to load image from: static/media/DE_HOYER_33_PRO.jpeg
2025-06-18 10:22:49,402 - app.main - INFO - Successfully inserted image for row 23
2025-06-18 10:22:49,403 - app.main - INFO - Attempting to load image from: static/media/DE_HOYER_11_PRO.jpeg
2025-06-18 10:22:49,408 - app.main - INFO - Successfully inserted image for row 24
2025-06-18 10:22:49,408 - app.main - INFO - Attempting to load image from: static/media/DE_WESTFALENGAS_33_PRO.jpeg
2025-06-18 10:22:49,410 - app.main - INFO - Successfully inserted image for row 25
2025-06-18 10:22:49,411 - app.main - INFO - Attempting to load image from: static/media/DE_WESTFALENGAS_11_PRO.jpg
2025-06-18 10:22:49,413 - app.main - INFO - Successfully inserted image for row 26
2025-06-18 10:22:49,413 - app.main - INFO - Attempting to load image from: static/media/DE_TYCKZA_33_PRO.jpg
2025-06-18 10:22:49,415 - app.main - INFO - Successfully inserted image for row 27
2025-06-18 10:22:49,415 - app.main - INFO - Attempting to load image from: static/media/DE_TYCKZA_11_PRO.jpeg
2025-06-18 10:22:49,418 - app.main - INFO - Successfully inserted image for row 28
2025-06-18 10:22:49,418 - app.main - INFO - Attempting to load image from: static/media/FR_VITOGAZ_35_PRO.png
2025-06-18 10:22:49,423 - app.main - INFO - Successfully inserted image for row 29
2025-06-18 10:22:49,424 - app.main - INFO - Attempting to load image from: static/media/FR_VITOGAZ_13_PRO.png
2025-06-18 10:22:49,428 - app.main - INFO - Successfully inserted image for row 30
2025-06-18 10:22:49,428 - app.main - INFO - Attempting to load image from: static/media/FR_VITOGAZ_13_BUT.png
2025-06-18 10:22:49,433 - app.main - INFO - Successfully inserted image for row 31
2025-06-18 10:22:49,433 - app.main - INFO - Attempting to load image from: static/media/FR_UNERGIES_13_PRO.png
2025-06-18 10:22:49,438 - app.main - INFO - Successfully inserted image for row 32
2025-06-18 10:22:49,438 - app.main - INFO - Attempting to load image from: static/media/FR_UNERGIES_13_BUT.png
2025-06-18 10:22:49,441 - app.main - INFO - Successfully inserted image for row 33
2025-06-18 10:22:49,442 - app.main - INFO - Attempting to load image from: static/media/FR_PRIMAGAZ_35_PRO.jpeg
2025-06-18 10:22:49,444 - app.main - INFO - Successfully inserted image for row 34
2025-06-18 10:22:49,444 - app.main - INFO - Attempting to load image from: static/media/FR_PRIMAGAZ_13_PRO.jpeg
2025-06-18 10:22:49,446 - app.main - INFO - Successfully inserted image for row 35
2025-06-18 10:22:49,446 - app.main - INFO - Attempting to load image from: static/media/FR_PRIMAGAZ_13_BUT.jpeg
2025-06-18 10:22:49,448 - app.main - INFO - Successfully inserted image for row 36
2025-06-18 10:22:49,448 - app.main - INFO - Attempting to load image from: static/media/FR_EHO_35_PRO.png
2025-06-18 10:22:49,450 - app.main - INFO - Successfully inserted image for row 37
2025-06-18 10:22:49,450 - app.main - INFO - Attempting to load image from: static/media/FR_EHO_13_PRO.png
2025-06-18 10:22:49,455 - app.main - INFO - Successfully inserted image for row 38
2025-06-18 10:22:49,455 - app.main - INFO - Attempting to load image from: static/media/FR_EHO_12-5_BUT.png
2025-06-18 10:22:49,459 - app.main - INFO - Successfully inserted image for row 39
2025-06-18 10:22:49,459 - app.main - INFO - Attempting to load image from: static/media/FR_CLAIRGAZ_11_PRO.jpg
2025-06-18 10:22:49,460 - app.main - INFO - Successfully inserted image for row 40
2025-06-18 10:22:49,461 - app.main - INFO - Attempting to load image from: static/media/FR_CLAIRGAZ_13_BUT.jpg
2025-06-18 10:22:49,469 - app.main - INFO - Successfully inserted image for row 41
2025-06-18 10:22:49,469 - app.main - INFO - Attempting to load image from: static/media/FR_CARREFOUR_13_BUT.jpg
2025-06-18 10:22:49,522 - app.main - INFO - Successfully inserted image for row 42
2025-06-18 10:22:49,523 - app.main - INFO - Attempting to load image from: static/media/FR_BUTAGAZ_35_PRO.png
2025-06-18 10:22:49,526 - app.main - INFO - Successfully inserted image for row 43
2025-06-18 10:22:49,526 - app.main - INFO - Attempting to load image from: static/media/FR_BUTAGAZ_13_PRO.png
2025-06-18 10:22:49,530 - app.main - INFO - Successfully inserted image for row 44
2025-06-18 10:22:49,530 - app.main - INFO - Attempting to load image from: static/media/FR_BUTAGAZ_L10_BUT.png
2025-06-18 10:22:49,533 - app.main - INFO - Successfully inserted image for row 45
2025-06-18 10:22:49,534 - app.main - INFO - Attempting to load image from: static/media/FR_BUTAGAZ_13_BUT.png
2025-06-18 10:22:49,538 - app.main - INFO - Successfully inserted image for row 46
2025-06-18 10:22:49,538 - app.main - INFO - Attempting to load image from: static/media/FR_AUCHAN_13_PRO.png
2025-06-18 10:22:49,541 - app.main - INFO - Successfully inserted image for row 47
2025-06-18 10:22:49,541 - app.main - INFO - Attempting to load image from: static/media/FR_AUCHAN_13_BUT.png
2025-06-18 10:22:49,544 - app.main - INFO - Successfully inserted image for row 48
2025-06-18 10:22:49,544 - app.main - INFO - Attempting to load image from: static/media/FR_ANTARGAZ_35_PRO.png
2025-06-18 10:22:49,547 - app.main - INFO - Successfully inserted image for row 49
2025-06-18 10:22:49,547 - app.main - INFO - Attempting to load image from: static/media/FR_ANTARGAZ_13_PRO.png
2025-06-18 10:22:49,552 - app.main - INFO - Successfully inserted image for row 50
2025-06-18 10:22:49,553 - app.main - INFO - Attempting to load image from: static/media/FR_ANTARGAZ_L10_BUT.png
2025-06-18 10:22:49,558 - app.main - INFO - Successfully inserted image for row 51
2025-06-18 10:22:49,558 - app.main - INFO - Attempting to load image from: static/media/FR_ANTARGAZ_13_BUT.png
2025-06-18 10:22:49,561 - app.main - INFO - Successfully inserted image for row 52
2025-06-18 10:22:49,561 - app.main - INFO - Attempting to load image from: static/media/UK_FLOGAS_47_PRO.jpeg
2025-06-18 10:22:49,563 - app.main - INFO - Successfully inserted image for row 53
2025-06-18 10:22:49,563 - app.main - INFO - Attempting to load image from: static/media/UK_FLOGAS_19_PRO.jpeg
2025-06-18 10:22:49,565 - app.main - INFO - Successfully inserted image for row 54
2025-06-18 10:22:49,565 - app.main - INFO - Attempting to load image from: static/media/UK_FLOGAS_13(21mm)_BUT.jpg
2025-06-18 10:22:49,567 - app.main - INFO - Successfully inserted image for row 55
2025-06-18 10:22:49,567 - app.main - INFO - Attempting to load image from: static/media/UK_FLOGAS_13(20mm)_BUT.jpg
2025-06-18 10:22:49,570 - app.main - INFO - Successfully inserted image for row 56
2025-06-18 10:22:49,570 - app.main - INFO - Attempting to load image from: static/media/UK_FLOGAS_11_PRO.jpeg
2025-06-18 10:22:49,572 - app.main - INFO - Successfully inserted image for row 57
2025-06-18 10:22:49,572 - app.main - INFO - Attempting to load image from: static/media/UK_FLOGAS_L10_PRO.png
2025-06-18 10:22:49,576 - app.main - INFO - Successfully inserted image for row 58
2025-06-18 10:22:49,576 - app.main - INFO - Attempting to load image from: static/media/UK_EXTRAGAS_46_PRO.png
2025-06-18 10:22:49,580 - app.main - INFO - Successfully inserted image for row 59
2025-06-18 10:22:49,580 - app.main - INFO - Attempting to load image from: static/media/UK_EXTRAGAS_19_PRO.png
2025-06-18 10:22:49,585 - app.main - INFO - Successfully inserted image for row 60
2025-06-18 10:22:49,585 - app.main - INFO - Attempting to load image from: static/media/UK_EXTRAGAS_11_PRO.png
2025-06-18 10:22:49,590 - app.main - INFO - Successfully inserted image for row 61
2025-06-18 10:22:49,590 - app.main - INFO - Attempting to load image from: static/media/UK_EXTRAGAS_12_BUT.png
2025-06-18 10:22:49,594 - app.main - INFO - Successfully inserted image for row 62
2025-06-18 10:22:49,595 - app.main - INFO - Attempting to load image from: static/media/UK_CALOR_47_PRO.jpeg
2025-06-18 10:22:49,597 - app.main - INFO - Successfully inserted image for row 63
2025-06-18 10:22:49,597 - app.main - INFO - Attempting to load image from: static/media/UK_CALOR_19_PRO.jpeg
2025-06-18 10:22:49,600 - app.main - INFO - Successfully inserted image for row 64
2025-06-18 10:22:49,600 - app.main - INFO - Attempting to load image from: static/media/UK_CALOR_13_PRO.jpeg
2025-06-18 10:22:49,603 - app.main - INFO - Successfully inserted image for row 65
2025-06-18 10:22:49,603 - app.main - INFO - Attempting to load image from: static/media/UK_CALOR_15_BUT.jpeg
2025-06-18 10:22:49,607 - app.main - INFO - Successfully inserted image for row 66
2025-06-18 10:22:49,607 - app.main - INFO - Attempting to load image from: static/media/CH_VITOGAZ_L10_PRO.jpeg
2025-06-18 10:22:49,610 - app.main - INFO - Successfully inserted image for row 67
2025-06-18 10:22:49,610 - app.main - INFO - Attempting to load image from: static/media/ES_REPSOL_11_PRO.jpeg
2025-06-18 10:22:49,612 - app.main - INFO - Successfully inserted image for row 68
2025-06-18 10:22:49,612 - app.main - INFO - Attempting to load image from: static/media/ES_REPSOL_12-5_BUT.jpeg
2025-06-18 10:22:49,614 - app.main - INFO - Successfully inserted image for row 69
2025-06-18 10:22:49,614 - app.main - INFO - Attempting to load image from: static/media/PT_GALP_10_PRO.png
2025-06-18 10:22:49,617 - app.main - INFO - Successfully inserted image for row 70
2025-06-18 10:22:49,617 - app.main - INFO - Attempting to load image from: static/media/CH_FLAGA_35_PRO.jpeg
2025-06-18 10:22:49,619 - app.main - INFO - Successfully inserted image for row 71
2025-06-18 10:22:49,620 - app.main - INFO - Attempting to load image from: static/media/CH_FLAGA_L10_PRO.jpeg
2025-06-18 10:22:49,623 - app.main - INFO - Successfully inserted image for row 72
2025-06-18 10:22:49,623 - app.main - INFO - Attempting to load image from: static/media/CH_FLAGA_10-5_PRO.jpeg
2025-06-18 10:22:49,625 - app.main - INFO - Successfully inserted image for row 73
2025-06-18 10:22:49,625 - app.main - INFO - Attempting to load image from: static/media/CH_GLOBALGAS_33_PRO.jpeg
2025-06-18 10:22:49,628 - app.main - INFO - Successfully inserted image for row 74
2025-06-18 10:22:49,629 - app.main - INFO - Attempting to load image from: static/media/CH_GLOBALGAS_11_PRO.jpeg
2025-06-18 10:22:49,632 - app.main - INFO - Successfully inserted image for row 75
2025-06-18 10:22:49,632 - app.main - INFO - Attempting to load image from: static/media/CH_VITOGAZ_35_PRO.jpeg
2025-06-18 10:22:49,636 - app.main - INFO - Successfully inserted image for row 76
2025-06-18 10:22:49,636 - app.main - INFO - Attempting to load image from: static/media/CH_VITOGAZ_10-5_PRO.jpeg
2025-06-18 10:22:49,642 - app.main - INFO - Successfully inserted image for row 77
2025-06-18 10:22:49,642 - app.main - INFO - Attempting to load image from: static/media/ES_REPSOL_L12_BUT.jpeg
2025-06-18 10:22:49,645 - app.main - INFO - Successfully inserted image for row 78
2025-06-18 10:22:49,645 - app.main - INFO - Attempting to load image from: static/media/ES_REPSOL_35_PRO.jpeg
2025-06-18 10:22:49,647 - app.main - INFO - Successfully inserted image for row 79
2025-06-18 10:22:49,647 - app.main - INFO - Attempting to load image from: static/media/ES_CEPSA_11_PRO.jpeg
2025-06-18 10:22:49,648 - app.main - INFO - Successfully inserted image for row 80
2025-06-18 10:22:49,648 - app.main - INFO - Attempting to load image from: static/media/ES_CEPSA_L11_PRO.jpeg
2025-06-18 10:22:49,650 - app.main - INFO - Successfully inserted image for row 81
2025-06-18 10:22:49,650 - app.main - INFO - Attempting to load image from: static/media/ES_CEPSA_L12-5_BUT.jpeg
2025-06-18 10:22:49,653 - app.main - INFO - Successfully inserted image for row 82
2025-06-18 10:22:49,654 - app.main - INFO - Attempting to load image from: static/media/ES_GALP_35_PRO.png
2025-06-18 10:22:49,655 - app.main - INFO - Successfully inserted image for row 83
2025-06-18 10:22:49,655 - app.main - INFO - Attempting to load image from: static/media/ES_GALP_11_PRO.png
2025-06-18 10:22:49,656 - app.main - INFO - Successfully inserted image for row 84
2025-06-18 10:22:49,656 - app.main - INFO - Attempting to load image from: static/media/ES_GALP_P12_BUT.png
2025-06-18 10:22:49,660 - app.main - INFO - Successfully inserted image for row 85
2025-06-18 10:22:49,660 - app.main - INFO - Attempting to load image from: static/media/ES_GALP_12.5_BUT.png
2025-06-18 10:22:49,661 - app.main - INFO - Successfully inserted image for row 86
2025-06-18 10:22:49,661 - app.main - INFO - Attempting to load image from: static/media/PT_TUTIGAS_45_PRO.jpeg
2025-06-18 10:22:49,663 - app.main - INFO - Successfully inserted image for row 87
2025-06-18 10:22:49,663 - app.main - INFO - Attempting to load image from: static/media/PT_TUTIGAS_11_PRO.jpeg
2025-06-18 10:22:49,666 - app.main - INFO - Successfully inserted image for row 88
2025-06-18 10:22:49,666 - app.main - INFO - Attempting to load image from: static/media/PT_TUTIGAS_13_BUT.jpeg
2025-06-18 10:22:49,668 - app.main - INFO - Successfully inserted image for row 89
2025-06-18 10:22:49,669 - app.main - INFO - Attempting to load image from: static/media/PT_TUTIGAS_XL12-5_BUT.png
2025-06-18 10:22:49,673 - app.main - INFO - Successfully inserted image for row 90
2025-06-18 10:22:49,673 - app.main - INFO - Attempting to load image from: static/media/PT_PRIO_45_PRO.jpeg
2025-06-18 10:22:49,677 - app.main - INFO - Successfully inserted image for row 91
2025-06-18 10:22:49,677 - app.main - INFO - Attempting to load image from: static/media/PT_PRIO_9_BUT.jpeg
2025-06-18 10:22:49,680 - app.main - INFO - Successfully inserted image for row 92
2025-06-18 10:22:49,680 - app.main - INFO - Attempting to load image from: static/media/PT_OZ_45_PRO.png
2025-06-18 10:22:49,685 - app.main - INFO - Successfully inserted image for row 93
2025-06-18 10:22:49,685 - app.main - INFO - Attempting to load image from: static/media/PT_OZ_11_PRO.png
2025-06-18 10:22:49,690 - app.main - INFO - Successfully inserted image for row 94
2025-06-18 10:22:49,691 - app.main - INFO - Attempting to load image from: static/media/PT_OZ_13_BUT.png
2025-06-18 10:22:49,695 - app.main - INFO - Successfully inserted image for row 95
2025-06-18 10:22:49,695 - app.main - INFO - Attempting to load image from: static/media/PT_OZ_P13_BUT.png
2025-06-18 10:22:49,701 - app.main - INFO - Successfully inserted image for row 96
2025-06-18 10:22:49,702 - app.main - INFO - Attempting to load image from: static/media/PT_GALP_45_PRO.png
2025-06-18 10:22:49,704 - app.main - INFO - Successfully inserted image for row 97
2025-06-18 10:22:49,704 - app.main - INFO - Attempting to load image from: static/media/PT_GALP_11_PRO.png
2025-06-18 10:22:49,706 - app.main - INFO - Successfully inserted image for row 98
2025-06-18 10:22:49,707 - app.main - INFO - Attempting to load image from: static/media/PT_GALP_13_BUT.png
2025-06-18 10:22:49,708 - app.main - INFO - Successfully inserted image for row 99
2025-06-18 10:22:49,708 - app.main - INFO - Attempting to load image from: static/media/PT_GALP_12_BUT.png
2025-06-18 10:22:49,711 - app.main - INFO - Successfully inserted image for row 100
2025-06-18 10:22:49,712 - app.main - INFO - Attempting to load image from: static/media/PT_CEPSA_45_PRO.png
2025-06-18 10:22:49,715 - app.main - INFO - Successfully inserted image for row 101
2025-06-18 10:22:49,715 - app.main - INFO - Attempting to load image from: static/media/PT_CEPSA_35_PRO.png
2025-06-18 10:22:49,718 - app.main - INFO - Successfully inserted image for row 102
2025-06-18 10:22:49,718 - app.main - INFO - Attempting to load image from: static/media/PT_CEPSA_11_PRO.png
2025-06-18 10:22:49,722 - app.main - INFO - Successfully inserted image for row 103
2025-06-18 10:22:49,722 - app.main - INFO - Attempting to load image from: static/media/PT_CEPSA_12-5_BUT.png
2025-06-18 10:22:49,725 - app.main - INFO - Successfully inserted image for row 104
2025-06-18 10:22:49,725 - app.main - INFO - Attempting to load image from: static/media/PT_REPSOL_45_PRO.png
2025-06-18 10:22:49,726 - app.main - INFO - Successfully inserted image for row 105
2025-06-18 10:22:49,726 - app.main - INFO - Attempting to load image from: static/media/PT_REPSOL_35_PRO.png
2025-06-18 10:22:49,727 - app.main - INFO - Successfully inserted image for row 106
2025-06-18 10:22:49,727 - app.main - INFO - Attempting to load image from: static/media/PT_RUBIS_45_PRO.png
2025-06-18 10:22:49,729 - app.main - INFO - Successfully inserted image for row 107
2025-06-18 10:22:49,730 - app.main - INFO - Attempting to load image from: static/media/PT_RUBIS_11_PRO.png
2025-06-18 10:22:49,732 - app.main - INFO - Successfully inserted image for row 108
2025-06-18 10:22:49,733 - app.main - INFO - Attempting to load image from: static/media/PT_RUBIS_13_BUT.png
2025-06-18 10:22:49,736 - app.main - INFO - Successfully inserted image for row 109
2025-06-18 10:22:49,737 - app.main - INFO - Attempting to load image from: static/media/PT_RUBIS_L12_BUT.png
2025-06-18 10:22:49,740 - app.main - INFO - Successfully inserted image for row 110
2025-06-18 10:22:49,741 - app.main - INFO - Attempting to load image from: static/media/ES_REPSOL_35_PRO.jpeg
2025-06-18 10:22:49,743 - app.main - INFO - Successfully inserted image for row 111
2025-06-18 10:22:49,743 - app.main - INFO - Attempting to load image from: static/media/ES_REPSOL_11_PRO.jpeg
2025-06-18 10:22:49,745 - app.main - INFO - Successfully inserted image for row 112
2025-06-18 10:22:49,746 - app.main - INFO - Attempting to load image from: static/media/ES_REPSOL_12-5_BUT.jpeg
2025-06-18 10:22:49,748 - app.main - INFO - Successfully inserted image for row 113
2025-06-18 10:22:49,748 - app.main - INFO - Attempting to load image from: static/media/PT_REPSOL_45_PRO.png
2025-06-18 10:22:49,749 - app.main - INFO - Successfully inserted image for row 114
2025-06-18 10:22:49,749 - app.main - INFO - Attempting to load image from: static/media/PT_REPSOL_35_PRO.png
2025-06-18 10:22:49,750 - app.main - INFO - Successfully inserted image for row 115
2025-06-18 10:22:49,750 - app.main - INFO - Attempting to load image from: static/media/PT_REPSOL_11_PRO.png
2025-06-18 10:22:49,751 - app.main - INFO - Successfully inserted image for row 116
2025-06-18 10:22:49,752 - app.main - INFO - Attempting to load image from: static/media/PT_REPSOL_13_BUT.png
2025-06-18 10:22:49,753 - app.main - INFO - Successfully inserted image for row 117
2025-06-18 10:22:49,754 - app.main - INFO - Attempting to load image from: static/media/PT_REPSOL_12-5_BUT.png
2025-06-18 10:22:49,755 - app.main - INFO - Successfully inserted image for row 118
2025-06-18 10:22:49,755 - app.main - INFO - Attempting to load image from: static/media/PT_REPSOL_K11_BUT.png
2025-06-18 10:22:49,756 - app.main - INFO - Successfully inserted image for row 119
2025-06-18 10:22:49,757 - app.main - INFO - Attempting to load image from: static/media/PT_RUBIS_45_PRO.png
2025-06-18 10:22:49,759 - app.main - INFO - Successfully inserted image for row 120
2025-06-18 10:22:49,759 - app.main - INFO - Attempting to load image from: static/media/PT_RUBIS_11_PRO.png
2025-06-18 10:22:49,762 - app.main - INFO - Successfully inserted image for row 121
2025-06-18 10:22:49,762 - app.main - INFO - Attempting to load image from: static/media/PT_RUBIS_13_BUT.png
2025-06-18 10:22:49,765 - app.main - INFO - Successfully inserted image for row 122
2025-06-18 10:22:53,857 - app.services.email_service - INFO - Email template directory: C:\Users\<USER>\Downloads\Rubis-Bottle-Data-codespace-laughing-happiness-vxr446x59xx3x4pq\Rubis-Bottle-Data-codespace-laughing-happiness-vxr446x59xx3x4pq\templates\email
2025-06-18 10:22:59,067 - app.services.email_service - INFO - Email <NAME_EMAIL> with subject: Gas Prices Report - Generated
2025-06-18 10:22:59,101 - app.main - INFO - Report generation email sent successfully
2025-06-18 10:22:59,105 - app.middleware.performance - WARNING - Slow request: GET /generate-report took 10.071s from testclient
2025-06-18 10:22:59,110 - httpx - INFO - HTTP Request: GET http://testserver/generate-report "HTTP/1.1 200 OK"
2025-06-18 10:22:59,157 - app.services.email_service - INFO - Email template directory: C:\Users\<USER>\Downloads\Rubis-Bottle-Data-codespace-laughing-happiness-vxr446x59xx3x4pq\Rubis-Bottle-Data-codespace-laughing-happiness-vxr446x59xx3x4pq\templates\email
2025-06-18 10:22:59,168 - app.services.email_service - INFO - Successfully rendered template: report_template.html
2025-06-18 10:23:00,720 - app.services.email_service - INFO - Email <NAME_EMAIL> with subject: Gas Price Report - 2025-06-18
2025-06-18 10:23:00,750 - app.services.email_service - INFO - Report email sent successfully with 1 attachments
2025-06-18 10:23:00,752 - httpx - INFO - HTTP Request: GET http://testserver/generate-email-report "HTTP/1.1 200 OK"
2025-06-27 16:36:33,329 - app.services.email_service - INFO - Logging initialized
2025-06-27 16:36:33,587 - httpx - INFO - HTTP Request: GET http://testserver/login "HTTP/1.1 200 OK"
2025-06-27 16:36:33,595 - app.services.auth_service - INFO - User admin logged in successfully
2025-06-27 16:36:33,598 - httpx - INFO - HTTP Request: POST http://testserver/auth/login "HTTP/1.1 200 OK"
2025-06-27 16:36:33,605 - httpx - INFO - HTTP Request: GET http://testserver/login "HTTP/1.1 302 Found"
2025-06-27 16:36:33,622 - httpx - INFO - HTTP Request: GET http://testserver/manual-request "HTTP/1.1 200 OK"
2025-06-27 16:36:33,630 - app.services.auth_service - INFO - User admin logged in successfully
2025-06-27 16:36:33,632 - httpx - INFO - HTTP Request: POST http://testserver/auth/login "HTTP/1.1 200 OK"
2025-06-27 16:36:33,641 - httpx - INFO - HTTP Request: POST http://testserver/auth/login "HTTP/1.1 401 Unauthorized"
2025-06-27 16:36:33,650 - httpx - INFO - HTTP Request: POST http://testserver/auth/login "HTTP/1.1 400 Bad Request"
2025-06-27 16:36:33,658 - httpx - INFO - HTTP Request: POST http://testserver/auth/login "HTTP/1.1 400 Bad Request"
2025-06-27 16:36:33,667 - app.services.auth_service - INFO - User admin logged in successfully
2025-06-27 16:36:33,671 - httpx - INFO - HTTP Request: POST http://testserver/auth/login "HTTP/1.1 200 OK"
2025-06-27 16:36:33,675 - app.services.auth_service - INFO - User admin logged out successfully
2025-06-27 16:36:33,679 - httpx - INFO - HTTP Request: POST http://testserver/auth/logout "HTTP/1.1 302 Found"
2025-06-27 16:36:33,688 - httpx - INFO - HTTP Request: GET http://testserver/login "HTTP/1.1 200 OK"
2025-06-27 16:36:33,698 - httpx - INFO - HTTP Request: GET http://testserver/manual-request "HTTP/1.1 302 Found"
2025-06-27 16:36:33,707 - httpx - INFO - HTTP Request: GET http://testserver/login "HTTP/1.1 200 OK"
2025-06-27 16:36:33,713 - app.services.auth_service - INFO - User admin logged in successfully
2025-06-27 16:36:33,715 - httpx - INFO - HTTP Request: POST http://testserver/auth/login "HTTP/1.1 200 OK"
2025-06-27 16:36:33,724 - httpx - INFO - HTTP Request: GET http://testserver/manual-request "HTTP/1.1 200 OK"
2025-06-27 16:36:33,734 - httpx - INFO - HTTP Request: POST http://testserver/run-scraper "HTTP/1.1 401 Unauthorized"
2025-06-27 16:36:33,744 - app.services.auth_service - INFO - User admin logged in successfully
2025-06-27 16:36:33,747 - httpx - INFO - HTTP Request: POST http://testserver/auth/login "HTTP/1.1 200 OK"
2025-06-27 16:36:33,805 - app.services.monitoring_service - INFO - Started scraping operation: scrape_20250627_163633
2025-06-27 16:36:35,313 - app.services.scraper_service - INFO - Processing England
2025-06-27 16:36:44,123 - app.services.scraper_service - INFO - Screenshot saved: England_Calor_INITIALSCREEN.png
2025-06-27 16:36:44,498 - app.services.scraper_service - INFO - Screenshot saved: England_Calor_BOTTLE_SCREEN.png
2025-06-27 16:36:50,575 - app.services.scraper_service - INFO - Screenshot saved: England_Calor_INITIALSCREEN.png
2025-06-27 16:36:50,710 - app.services.scraper_service - INFO - Screenshot saved: England_Calor_BOTTLE_SCREEN.png
2025-06-27 16:36:55,251 - app.services.scraper_service - INFO - Screenshot saved: England_Calor_INITIALSCREEN.png
2025-06-27 16:36:55,391 - app.services.scraper_service - INFO - Screenshot saved: England_Calor_BOTTLE_SCREEN.png
2025-06-27 16:36:59,960 - app.services.scraper_service - INFO - Screenshot saved: England_Calor_INITIALSCREEN.png
2025-06-27 16:37:00,105 - app.services.scraper_service - INFO - Screenshot saved: England_Calor_BOTTLE_SCREEN.png
2025-06-27 16:37:03,288 - app.services.scraper_service - INFO - Screenshot saved: England_Extragas_INITIALSCREEN.png
2025-06-27 16:37:03,482 - app.services.scraper_service - INFO - Screenshot saved: England_Extragas_BOTTLE_SCREEN.png
2025-06-27 16:37:06,597 - app.services.scraper_service - INFO - Screenshot saved: England_Extragas_INITIALSCREEN.png
2025-06-27 16:37:06,788 - app.services.scraper_service - INFO - Screenshot saved: England_Extragas_BOTTLE_SCREEN.png
2025-06-27 16:37:09,807 - app.services.scraper_service - INFO - Screenshot saved: England_Extragas_INITIALSCREEN.png
2025-06-27 16:37:09,978 - app.services.scraper_service - INFO - Screenshot saved: England_Extragas_BOTTLE_SCREEN.png
2025-06-27 16:37:12,948 - app.services.scraper_service - INFO - Screenshot saved: England_Extragas_INITIALSCREEN.png
2025-06-27 16:37:13,125 - app.services.scraper_service - INFO - Screenshot saved: England_Extragas_BOTTLE_SCREEN.png
2025-06-27 16:37:17,123 - app.services.scraper_service - INFO - Screenshot saved: England_Flogas_INITIALSCREEN.png
2025-06-27 16:37:17,335 - app.services.scraper_service - INFO - Screenshot saved: England_Flogas_BOTTLE_SCREEN.png
2025-06-27 16:37:21,444 - app.services.scraper_service - INFO - Screenshot saved: England_Flogas_INITIALSCREEN.png
2025-06-27 16:37:21,708 - app.services.scraper_service - INFO - Screenshot saved: England_Flogas_BOTTLE_SCREEN.png
2025-06-27 16:37:25,793 - app.services.scraper_service - INFO - Screenshot saved: England_Flogas_INITIALSCREEN.png
2025-06-27 16:37:26,023 - app.services.scraper_service - INFO - Screenshot saved: England_Flogas_BOTTLE_SCREEN.png
2025-06-27 16:37:29,834 - app.services.scraper_service - INFO - Screenshot saved: England_Flogas_INITIALSCREEN.png
2025-06-27 16:37:30,127 - app.services.scraper_service - INFO - Screenshot saved: England_Flogas_BOTTLE_SCREEN.png
2025-06-27 16:37:31,799 - app.services.scraper_service - WARNING - Error processing website Flogas: Page.wait_for_timeout: Target page, context or browser has been closed
2025-06-27 16:37:46,831 - app.services.email_service - INFO - Logging initialized
2025-06-27 16:37:46,885 - app.db.database - INFO - Database initialized successfully
2025-06-27 16:37:46,886 - app.services.scheduler_service - INFO - Starting scheduler at 2025-06-27T16:37:46.886860
2025-06-27 16:37:46,887 - app.services.scheduler_service - INFO - Scheduler started in background thread
2025-06-27 16:37:46,887 - app.main - INFO - Application started successfully
2025-06-27 16:37:46,889 - app.services.scheduler_service - INFO - Job scheduled to run daily at 15:08, next run: 2025-06-28 15:08:00
2025-06-27 16:39:17,050 - app.services.auth_service - INFO - User admin logged in successfully
2025-06-27 16:45:29,218 - app.services.scheduler_service - INFO - Scheduler stop requested
2025-06-27 16:45:29,218 - app.main - INFO - Application shutdown successfully
2025-06-27 16:45:34,479 - app.services.email_service - INFO - Logging initialized
2025-06-27 16:45:34,536 - app.db.database - INFO - Database initialized successfully
2025-06-27 16:45:34,537 - app.services.scheduler_service - INFO - Starting scheduler at 2025-06-27T16:45:34.537224
2025-06-27 16:45:34,537 - app.services.scheduler_service - INFO - Scheduler started in background thread
2025-06-27 16:45:34,537 - app.main - INFO - Application started successfully
2025-06-27 16:45:34,540 - app.services.scheduler_service - INFO - Job scheduled to run daily at 15:08, next run: 2025-06-28 15:08:00
2025-06-27 16:45:46,986 - app.services.email_service - INFO - Logging initialized
2025-06-27 16:45:47,026 - app.db.database - INFO - Database initialized successfully
2025-06-27 16:45:47,026 - app.services.scheduler_service - INFO - Starting scheduler at 2025-06-27T16:45:47.026965
2025-06-27 16:45:47,027 - app.services.scheduler_service - INFO - Scheduler started in background thread
2025-06-27 16:45:47,027 - app.main - INFO - Application started successfully
2025-06-27 16:45:47,028 - app.services.scheduler_service - INFO - Job scheduled to run daily at 15:08, next run: 2025-06-28 15:08:00
2025-06-27 16:47:41,726 - app.services.auth_service - INFO - User admin logged in successfully
2025-06-27 16:48:32,888 - app.db.database - INFO - get_gas_prices called with: start_date=2025-05-28 00:00:00, end_date=2025-06-27 23:59:59, countries=None, companies=None, product_types=None
2025-06-27 16:48:32,888 - app.db.database - INFO - Executing query: SELECT * FROM gas_prices WHERE 1=1 AND date_recorded >= ? AND date_recorded <= ? ORDER BY date_recorded DESC
2025-06-27 16:48:32,888 - app.db.database - INFO - Query parameters: ['2025-05-28T00:00:00', '2025-06-27T23:59:59']
2025-06-27 16:48:33,009 - app.db.database - INFO - Query returned 1416 results
2025-06-27 16:48:33,010 - app.db.database - INFO - get_gas_prices called with: start_date=2025-05-28 00:00:00, end_date=2025-06-27 23:59:59, countries=None, companies=None, product_types=None
2025-06-27 16:48:33,010 - app.db.database - INFO - Executing query: SELECT * FROM gas_prices WHERE 1=1 AND date_recorded >= ? AND date_recorded <= ? ORDER BY date_recorded DESC
2025-06-27 16:48:33,010 - app.db.database - INFO - Query parameters: ['2025-05-28T00:00:00', '2025-06-27T23:59:59']
2025-06-27 16:48:33,043 - app.db.database - INFO - Query returned 1416 results
2025-06-27 16:48:33,526 - app.main - INFO - database_table endpoint called with: start_date=2025-05-28, end_date=2025-06-27, countries=, companies=, product_types=
2025-06-27 16:48:33,527 - app.main - INFO - Parsed filter lists: countries=None, companies=None, product_types=None
2025-06-27 16:48:33,527 - app.db.database - INFO - get_gas_prices called with: start_date=2025-05-28 00:00:00, end_date=2025-06-27 23:59:59, countries=None, companies=None, product_types=None
2025-06-27 16:48:33,527 - app.db.database - INFO - Executing query: SELECT * FROM gas_prices WHERE 1=1 AND date_recorded >= ? AND date_recorded <= ? ORDER BY date_recorded DESC
2025-06-27 16:48:33,527 - app.db.database - INFO - Query parameters: ['2025-05-28T00:00:00', '2025-06-27T23:59:59']
2025-06-27 16:48:33,574 - app.db.database - INFO - Query returned 1416 results
2025-06-27 16:48:33,574 - app.main - INFO - Returning 1416 records to template
2025-06-27 16:49:38,910 - app.db.database - INFO - get_gas_prices called with: start_date=2025-05-28 00:00:00, end_date=2025-06-27 23:59:59, countries=None, companies=None, product_types=None
2025-06-27 16:49:38,911 - app.db.database - INFO - Executing query: SELECT * FROM gas_prices WHERE 1=1 AND date_recorded >= ? AND date_recorded <= ? ORDER BY date_recorded DESC
2025-06-27 16:49:38,911 - app.db.database - INFO - Query parameters: ['2025-05-28T00:00:00', '2025-06-27T23:59:59']
2025-06-27 16:49:38,947 - app.db.database - INFO - Query returned 1416 results
2025-06-27 16:49:38,948 - app.db.database - INFO - get_gas_prices called with: start_date=2025-05-28 00:00:00, end_date=2025-06-27 23:59:59, countries=None, companies=None, product_types=None
2025-06-27 16:49:38,948 - app.db.database - INFO - Executing query: SELECT * FROM gas_prices WHERE 1=1 AND date_recorded >= ? AND date_recorded <= ? ORDER BY date_recorded DESC
2025-06-27 16:49:38,948 - app.db.database - INFO - Query parameters: ['2025-05-28T00:00:00', '2025-06-27T23:59:59']
2025-06-27 16:49:38,977 - app.db.database - INFO - Query returned 1416 results
2025-06-27 16:49:39,389 - app.main - INFO - database_table endpoint called with: start_date=2025-05-28, end_date=2025-06-27, countries=, companies=, product_types=
2025-06-27 16:49:39,390 - app.main - INFO - Parsed filter lists: countries=None, companies=None, product_types=None
2025-06-27 16:49:39,390 - app.db.database - INFO - get_gas_prices called with: start_date=2025-05-28 00:00:00, end_date=2025-06-27 23:59:59, countries=None, companies=None, product_types=None
2025-06-27 16:49:39,390 - app.db.database - INFO - Executing query: SELECT * FROM gas_prices WHERE 1=1 AND date_recorded >= ? AND date_recorded <= ? ORDER BY date_recorded DESC
2025-06-27 16:49:39,391 - app.db.database - INFO - Query parameters: ['2025-05-28T00:00:00', '2025-06-27T23:59:59']
2025-06-27 16:49:39,434 - app.db.database - INFO - Query returned 1416 results
2025-06-27 16:49:39,435 - app.main - INFO - Returning 1416 records to template
2025-06-27 16:51:19,259 - app.services.scheduler_service - INFO - Scheduler stop requested
2025-06-27 16:51:19,260 - app.main - INFO - Application shutdown successfully
2025-06-27 16:52:21,241 - app.services.email_service - INFO - Logging initialized
2025-06-27 16:52:21,312 - app.db.database - INFO - Database initialized successfully
2025-06-27 16:52:21,313 - app.services.scheduler_service - INFO - Starting scheduler at 2025-06-27T16:52:21.313564
2025-06-27 16:52:21,313 - app.services.scheduler_service - INFO - Scheduler started in background thread
2025-06-27 16:52:21,314 - app.main - INFO - Application started successfully
2025-06-27 16:52:21,316 - app.services.scheduler_service - INFO - Job scheduled to run daily at 15:08, next run: 2025-06-28 15:08:00
2025-06-27 16:52:21,422 - app.services.monitoring_service - WARNING - Recorded error: http_request_error - name 'AuthService' is not defined
2025-06-27 16:52:21,423 - app.middleware.performance - ERROR - Request error: GET /manual-request failed after 0.008s: name 'AuthService' is not defined
2025-06-27 16:52:21,639 - app.services.monitoring_service - WARNING - Recorded error: http_request_error - 
2025-06-27 16:52:21,640 - app.middleware.performance - ERROR - Request error: GET /favicon.ico failed after 0.218s: 
2025-06-27 16:52:38,702 - app.services.scheduler_service - INFO - Scheduler stop requested
2025-06-27 16:52:38,703 - app.main - INFO - Application shutdown successfully
2025-06-27 16:52:43,443 - app.services.email_service - INFO - Logging initialized
2025-06-27 16:52:43,490 - app.db.database - INFO - Database initialized successfully
2025-06-27 16:52:43,491 - app.services.scheduler_service - INFO - Starting scheduler at 2025-06-27T16:52:43.491210
2025-06-27 16:52:43,491 - app.services.scheduler_service - INFO - Scheduler started in background thread
2025-06-27 16:52:43,491 - app.main - INFO - Application started successfully
2025-06-27 16:52:43,493 - app.services.scheduler_service - INFO - Job scheduled to run daily at 15:08, next run: 2025-06-28 15:08:00
2025-06-27 16:53:00,714 - app.services.scheduler_service - INFO - Scheduler stop requested
2025-06-27 16:53:00,715 - app.main - INFO - Application shutdown successfully
2025-06-27 16:53:06,729 - app.services.email_service - INFO - Logging initialized
2025-06-27 16:53:06,771 - app.db.database - INFO - Database initialized successfully
2025-06-27 16:53:06,772 - app.services.scheduler_service - INFO - Starting scheduler at 2025-06-27T16:53:06.772264
2025-06-27 16:53:06,772 - app.services.scheduler_service - INFO - Scheduler started in background thread
2025-06-27 16:53:06,772 - app.main - INFO - Application started successfully
2025-06-27 16:53:06,774 - app.services.scheduler_service - INFO - Job scheduled to run daily at 15:08, next run: 2025-06-28 15:08:00
2025-06-27 16:53:09,849 - app.services.scheduler_service - INFO - Scheduler stop requested
2025-06-27 16:53:09,849 - app.main - INFO - Application shutdown successfully
2025-06-27 16:53:15,402 - app.services.email_service - INFO - Logging initialized
2025-06-27 16:53:15,452 - app.db.database - INFO - Database initialized successfully
2025-06-27 16:53:15,453 - app.services.scheduler_service - INFO - Starting scheduler at 2025-06-27T16:53:15.452999
2025-06-27 16:53:15,453 - app.services.scheduler_service - INFO - Scheduler started in background thread
2025-06-27 16:53:15,453 - app.main - INFO - Application started successfully
2025-06-27 16:53:15,455 - app.services.scheduler_service - INFO - Job scheduled to run daily at 15:08, next run: 2025-06-28 15:08:00
2025-06-27 16:53:22,670 - app.services.scheduler_service - INFO - Scheduler stop requested
2025-06-27 16:53:22,670 - app.main - INFO - Application shutdown successfully
2025-06-27 16:53:40,421 - app.services.email_service - INFO - Logging initialized
2025-06-27 16:53:40,470 - app.db.database - INFO - Database initialized successfully
2025-06-27 16:53:40,471 - app.services.scheduler_service - INFO - Starting scheduler at 2025-06-27T16:53:40.471730
2025-06-27 16:53:40,471 - app.services.scheduler_service - INFO - Scheduler started in background thread
2025-06-27 16:53:40,472 - app.main - INFO - Application started successfully
2025-06-27 16:53:40,474 - app.services.scheduler_service - INFO - Job scheduled to run daily at 15:08, next run: 2025-06-28 15:08:00
2025-06-27 16:54:19,466 - app.services.scheduler_service - INFO - Scheduler stop requested
2025-06-27 16:54:19,466 - app.main - INFO - Application shutdown successfully
2025-06-27 16:54:23,848 - app.services.email_service - INFO - Logging initialized
2025-06-27 16:54:23,920 - app.db.database - INFO - Database initialized successfully
2025-06-27 16:54:23,921 - app.services.scheduler_service - INFO - Starting scheduler at 2025-06-27T16:54:23.921366
2025-06-27 16:54:23,921 - app.services.scheduler_service - INFO - Scheduler started in background thread
2025-06-27 16:54:23,921 - app.main - INFO - Application started successfully
2025-06-27 16:54:23,923 - app.services.scheduler_service - INFO - Job scheduled to run daily at 15:08, next run: 2025-06-28 15:08:00
2025-06-27 16:54:52,564 - app.services.scheduler_service - INFO - Scheduler stop requested
2025-06-27 16:54:52,564 - app.main - INFO - Application shutdown successfully
2025-06-27 16:54:59,786 - app.services.email_service - INFO - Logging initialized
2025-06-27 16:54:59,891 - app.db.database - INFO - Database initialized successfully
2025-06-27 16:54:59,895 - app.services.scheduler_service - INFO - Starting scheduler at 2025-06-27T16:54:59.895173
2025-06-27 16:54:59,895 - app.services.scheduler_service - INFO - Scheduler started in background thread
2025-06-27 16:54:59,940 - app.services.scheduler_service - INFO - Job scheduled to run daily at 15:08, next run: 2025-06-28 15:08:00
2025-06-27 16:54:59,945 - app.main - INFO - Application started successfully
2025-06-27 16:55:01,763 - app.services.scheduler_service - INFO - Scheduler stop requested
2025-06-27 16:55:01,764 - app.main - INFO - Application shutdown successfully
2025-06-27 16:55:10,253 - app.services.email_service - INFO - Logging initialized
2025-06-27 16:55:10,319 - app.db.database - INFO - Database initialized successfully
2025-06-27 16:55:10,320 - app.services.scheduler_service - INFO - Starting scheduler at 2025-06-27T16:55:10.320802
2025-06-27 16:55:10,320 - app.services.scheduler_service - INFO - Scheduler started in background thread
2025-06-27 16:55:10,321 - app.main - INFO - Application started successfully
2025-06-27 16:55:10,323 - app.services.scheduler_service - INFO - Job scheduled to run daily at 15:08, next run: 2025-06-28 15:08:00
2025-06-27 16:55:18,167 - app.services.scheduler_service - INFO - Scheduler stop requested
2025-06-27 16:55:18,167 - app.main - INFO - Application shutdown successfully
2025-06-27 16:55:24,383 - app.services.email_service - INFO - Logging initialized
2025-06-27 16:55:24,443 - app.db.database - INFO - Database initialized successfully
2025-06-27 16:55:24,444 - app.services.scheduler_service - INFO - Starting scheduler at 2025-06-27T16:55:24.444575
2025-06-27 16:55:24,444 - app.services.scheduler_service - INFO - Scheduler started in background thread
2025-06-27 16:55:24,445 - app.main - INFO - Application started successfully
2025-06-27 16:55:24,447 - app.services.scheduler_service - INFO - Job scheduled to run daily at 15:08, next run: 2025-06-28 15:08:00
2025-06-27 16:55:58,979 - app.services.scheduler_service - INFO - Scheduler stop requested
2025-06-27 16:55:58,980 - app.main - INFO - Application shutdown successfully
2025-06-27 16:56:04,368 - app.services.email_service - INFO - Logging initialized
2025-06-27 16:56:04,421 - app.db.database - INFO - Database initialized successfully
2025-06-27 16:56:04,422 - app.services.scheduler_service - INFO - Starting scheduler at 2025-06-27T16:56:04.422091
2025-06-27 16:56:04,422 - app.services.scheduler_service - INFO - Scheduler started in background thread
2025-06-27 16:56:04,422 - app.main - INFO - Application started successfully
2025-06-27 16:56:04,424 - app.services.scheduler_service - INFO - Job scheduled to run daily at 15:08, next run: 2025-06-28 15:08:00
2025-06-27 16:56:10,029 - app.services.scheduler_service - INFO - Scheduler stop requested
2025-06-27 16:56:10,029 - app.main - INFO - Application shutdown successfully
2025-06-27 16:56:14,778 - app.services.email_service - INFO - Logging initialized
2025-06-27 16:56:14,823 - app.db.database - INFO - Database initialized successfully
2025-06-27 16:56:14,824 - app.services.scheduler_service - INFO - Starting scheduler at 2025-06-27T16:56:14.824666
2025-06-27 16:56:14,824 - app.services.scheduler_service - INFO - Scheduler started in background thread
2025-06-27 16:56:14,825 - app.main - INFO - Application started successfully
2025-06-27 16:56:14,827 - app.services.scheduler_service - INFO - Job scheduled to run daily at 15:08, next run: 2025-06-28 15:08:00
2025-06-27 16:56:39,230 - app.services.scheduler_service - INFO - Scheduler stop requested
2025-06-27 16:56:39,231 - app.main - INFO - Application shutdown successfully
2025-06-27 16:56:44,876 - app.services.email_service - INFO - Logging initialized
2025-06-27 16:56:44,955 - app.db.database - INFO - Database initialized successfully
2025-06-27 16:56:44,956 - app.services.scheduler_service - INFO - Starting scheduler at 2025-06-27T16:56:44.956189
2025-06-27 16:56:44,956 - app.services.scheduler_service - INFO - Scheduler started in background thread
2025-06-27 16:56:44,957 - app.main - INFO - Application started successfully
2025-06-27 16:56:44,963 - app.services.scheduler_service - INFO - Job scheduled to run daily at 15:08, next run: 2025-06-28 15:08:00
2025-06-27 16:57:20,318 - app.services.scheduler_service - INFO - Scheduler stop requested
2025-06-27 16:57:20,318 - app.main - INFO - Application shutdown successfully
2025-06-27 16:57:24,021 - app.services.email_service - INFO - Logging initialized
2025-06-27 16:57:24,062 - app.db.database - INFO - Database initialized successfully
2025-06-27 16:57:24,062 - app.services.scheduler_service - INFO - Starting scheduler at 2025-06-27T16:57:24.062841
2025-06-27 16:57:24,063 - app.services.scheduler_service - INFO - Scheduler started in background thread
2025-06-27 16:57:24,063 - app.main - INFO - Application started successfully
2025-06-27 16:57:24,065 - app.services.scheduler_service - INFO - Job scheduled to run daily at 15:08, next run: 2025-06-28 15:08:00
2025-06-27 16:58:57,484 - app.services.scheduler_service - INFO - Scheduler stop requested
2025-06-27 16:58:57,484 - app.main - INFO - Application shutdown successfully
2025-06-27 17:09:51,985 - app.services.email_service - INFO - Logging initialized
2025-06-27 17:09:52,055 - app.db.database - INFO - Database initialized successfully
2025-06-27 17:09:52,056 - app.services.scheduler_service - INFO - Starting scheduler at 2025-06-27T17:09:52.055910
2025-06-27 17:09:52,056 - app.services.scheduler_service - INFO - Scheduler started in background thread
2025-06-27 17:09:52,056 - app.main - INFO - Application started successfully
2025-06-27 17:09:52,059 - app.services.scheduler_service - INFO - Job scheduled to run daily at 15:08, next run: 2025-06-28 15:08:00
2025-06-27 17:10:41,642 - app.services.scheduler_service - INFO - Scheduler stop requested
2025-06-27 17:10:41,642 - app.main - INFO - Application shutdown successfully
2025-06-27 17:10:49,176 - app.services.email_service - INFO - Logging initialized
2025-06-27 17:10:49,414 - app.db.database - INFO - Database initialized successfully
2025-06-27 17:10:49,445 - app.services.scheduler_service - INFO - Starting scheduler at 2025-06-27T17:10:49.445564
2025-06-27 17:10:49,446 - app.services.scheduler_service - INFO - Scheduler started in background thread
2025-06-27 17:10:49,449 - app.main - INFO - Application started successfully
2025-06-27 17:10:49,451 - app.services.scheduler_service - INFO - Job scheduled to run daily at 15:08, next run: 2025-06-28 15:08:00
2025-06-27 17:11:23,195 - app.db.database - INFO - get_gas_prices called with: start_date=2025-05-28 00:00:00, end_date=2025-06-27 23:59:59, countries=None, companies=None, product_types=None
2025-06-27 17:11:23,196 - app.db.database - INFO - Executing query: SELECT * FROM gas_prices WHERE 1=1 AND date_recorded >= ? AND date_recorded <= ? ORDER BY date_recorded DESC
2025-06-27 17:11:23,196 - app.db.database - INFO - Query parameters: ['2025-05-28T00:00:00', '2025-06-27T23:59:59']
2025-06-27 17:11:23,376 - app.db.database - INFO - Query returned 1416 results
2025-06-27 17:11:23,377 - app.db.database - INFO - get_gas_prices called with: start_date=2025-05-28 00:00:00, end_date=2025-06-27 23:59:59, countries=None, companies=None, product_types=None
2025-06-27 17:11:23,377 - app.db.database - INFO - Executing query: SELECT * FROM gas_prices WHERE 1=1 AND date_recorded >= ? AND date_recorded <= ? ORDER BY date_recorded DESC
2025-06-27 17:11:23,378 - app.db.database - INFO - Query parameters: ['2025-05-28T00:00:00', '2025-06-27T23:59:59']
2025-06-27 17:11:23,442 - app.db.database - INFO - Query returned 1416 results
2025-06-27 17:11:24,096 - app.main - INFO - database_table endpoint called with: start_date=2025-05-28, end_date=2025-06-27, countries=, companies=, product_types=
2025-06-27 17:11:24,097 - app.main - INFO - Parsed filter lists: countries=None, companies=None, product_types=None
2025-06-27 17:11:24,097 - app.db.database - INFO - get_gas_prices called with: start_date=2025-05-28 00:00:00, end_date=2025-06-27 23:59:59, countries=None, companies=None, product_types=None
2025-06-27 17:11:24,097 - app.db.database - INFO - Executing query: SELECT * FROM gas_prices WHERE 1=1 AND date_recorded >= ? AND date_recorded <= ? ORDER BY date_recorded DESC
2025-06-27 17:11:24,098 - app.db.database - INFO - Query parameters: ['2025-05-28T00:00:00', '2025-06-27T23:59:59']
2025-06-27 17:11:24,192 - app.db.database - INFO - Query returned 1416 results
2025-06-27 17:11:24,194 - app.main - INFO - Returning 1416 records to template
2025-06-27 17:11:27,736 - app.db.database - INFO - get_gas_prices called with: start_date=2025-05-28 00:00:00, end_date=2025-06-27 23:59:59, countries=None, companies=None, product_types=None
2025-06-27 17:11:27,736 - app.db.database - INFO - Executing query: SELECT * FROM gas_prices WHERE 1=1 AND date_recorded >= ? AND date_recorded <= ? ORDER BY date_recorded DESC
2025-06-27 17:11:27,737 - app.db.database - INFO - Query parameters: ['2025-05-28T00:00:00', '2025-06-27T23:59:59']
2025-06-27 17:11:27,914 - app.db.database - INFO - Query returned 1416 results
2025-06-27 17:11:28,013 - app.db.database - INFO - get_gas_prices called with: start_date=2025-05-28 00:00:00, end_date=2025-06-27 23:59:59, countries=None, companies=None, product_types=None
2025-06-27 17:11:28,013 - app.db.database - INFO - Executing query: SELECT * FROM gas_prices WHERE 1=1 AND date_recorded >= ? AND date_recorded <= ? ORDER BY date_recorded DESC
2025-06-27 17:11:28,013 - app.db.database - INFO - Query parameters: ['2025-05-28T00:00:00', '2025-06-27T23:59:59']
2025-06-27 17:11:28,130 - app.db.database - INFO - Query returned 1416 results
2025-06-27 17:11:29,078 - app.main - INFO - database_table endpoint called with: start_date=2025-05-28, end_date=2025-06-27, countries=, companies=, product_types=
2025-06-27 17:11:29,079 - app.main - INFO - Parsed filter lists: countries=None, companies=None, product_types=None
2025-06-27 17:11:29,079 - app.db.database - INFO - get_gas_prices called with: start_date=2025-05-28 00:00:00, end_date=2025-06-27 23:59:59, countries=None, companies=None, product_types=None
2025-06-27 17:11:29,080 - app.db.database - INFO - Executing query: SELECT * FROM gas_prices WHERE 1=1 AND date_recorded >= ? AND date_recorded <= ? ORDER BY date_recorded DESC
2025-06-27 17:11:29,080 - app.db.database - INFO - Query parameters: ['2025-05-28T00:00:00', '2025-06-27T23:59:59']
2025-06-27 17:11:29,149 - app.db.database - INFO - Query returned 1416 results
2025-06-27 17:11:29,150 - app.main - INFO - Returning 1416 records to template
2025-06-27 17:11:50,898 - app.services.scheduler_service - INFO - Scheduler stop requested
2025-06-27 17:11:50,899 - app.main - INFO - Application shutdown successfully
2025-06-27 17:11:54,472 - app.services.email_service - INFO - Logging initialized
2025-06-27 17:11:54,515 - app.db.database - INFO - Database initialized successfully
2025-06-27 17:11:54,516 - app.services.scheduler_service - INFO - Starting scheduler at 2025-06-27T17:11:54.516189
2025-06-27 17:11:54,516 - app.services.scheduler_service - INFO - Scheduler started in background thread
2025-06-27 17:11:54,516 - app.main - INFO - Application started successfully
2025-06-27 17:11:54,518 - app.services.scheduler_service - INFO - Job scheduled to run daily at 15:08, next run: 2025-06-28 15:08:00
2025-06-27 17:12:02,368 - app.services.scheduler_service - INFO - Scheduler stop requested
2025-06-27 17:12:02,368 - app.main - INFO - Application shutdown successfully
2025-06-27 17:12:06,063 - app.services.email_service - INFO - Logging initialized
2025-06-27 17:12:06,106 - app.db.database - INFO - Database initialized successfully
2025-06-27 17:12:06,107 - app.services.scheduler_service - INFO - Starting scheduler at 2025-06-27T17:12:06.107759
2025-06-27 17:12:06,107 - app.services.scheduler_service - INFO - Scheduler started in background thread
2025-06-27 17:12:06,108 - app.main - INFO - Application started successfully
2025-06-27 17:12:06,110 - app.services.scheduler_service - INFO - Job scheduled to run daily at 15:08, next run: 2025-06-28 15:08:00
2025-06-27 17:18:54,727 - app.services.scheduler_service - INFO - Scheduler stop requested
2025-06-27 17:18:54,728 - app.main - INFO - Application shutdown successfully
2025-06-27 17:19:01,158 - app.services.email_service - INFO - Logging initialized
2025-06-27 17:19:01,226 - app.db.database - INFO - Database initialized successfully
2025-06-27 17:19:01,228 - app.services.scheduler_service - INFO - Starting scheduler at 2025-06-27T17:19:01.227997
2025-06-27 17:19:01,228 - app.services.scheduler_service - INFO - Scheduler started in background thread
2025-06-27 17:19:01,228 - app.main - INFO - Application started successfully
2025-06-27 17:19:01,231 - app.services.scheduler_service - INFO - Job scheduled to run daily at 15:08, next run: 2025-06-28 15:08:00
2025-06-27 17:22:01,501 - app.services.scheduler_service - INFO - Scheduler stop requested
2025-06-27 17:22:01,502 - app.main - INFO - Application shutdown successfully
2025-06-27 17:22:10,979 - app.services.email_service - INFO - Logging initialized
2025-06-27 17:22:11,126 - app.db.database - INFO - Database initialized successfully
2025-06-27 17:22:11,128 - app.services.scheduler_service - INFO - Starting scheduler at 2025-06-27T17:22:11.128401
2025-06-27 17:22:11,128 - app.services.scheduler_service - INFO - Scheduler started in background thread
2025-06-27 17:22:11,129 - app.main - INFO - Application started successfully
2025-06-27 17:22:11,131 - app.services.scheduler_service - INFO - Job scheduled to run daily at 15:08, next run: 2025-06-28 15:08:00
2025-06-27 17:22:14,553 - app.services.scheduler_service - INFO - Scheduler stop requested
2025-06-27 17:22:14,554 - app.main - INFO - Application shutdown successfully
2025-06-27 17:22:21,692 - app.services.email_service - INFO - Logging initialized
2025-06-27 17:22:21,740 - app.db.database - INFO - Database initialized successfully
2025-06-27 17:22:21,741 - app.services.scheduler_service - INFO - Starting scheduler at 2025-06-27T17:22:21.741718
2025-06-27 17:22:21,741 - app.services.scheduler_service - INFO - Scheduler started in background thread
2025-06-27 17:22:21,742 - app.main - INFO - Application started successfully
2025-06-27 17:22:21,744 - app.services.scheduler_service - INFO - Job scheduled to run daily at 15:08, next run: 2025-06-28 15:08:00
2025-06-27 17:22:26,093 - app.services.scheduler_service - INFO - Scheduler stop requested
2025-06-27 17:22:26,094 - app.main - INFO - Application shutdown successfully
2025-06-27 17:22:30,705 - app.services.email_service - INFO - Logging initialized
2025-06-27 17:22:30,761 - app.db.database - INFO - Database initialized successfully
2025-06-27 17:22:30,762 - app.services.scheduler_service - INFO - Starting scheduler at 2025-06-27T17:22:30.762692
2025-06-27 17:22:30,762 - app.services.scheduler_service - INFO - Scheduler started in background thread
2025-06-27 17:22:30,763 - app.main - INFO - Application started successfully
2025-06-27 17:22:30,765 - app.services.scheduler_service - INFO - Job scheduled to run daily at 15:08, next run: 2025-06-28 15:08:00
2025-06-27 17:24:03,438 - app.db.database - INFO - get_gas_prices called with: start_date=2025-05-28 00:00:00, end_date=2025-06-27 23:59:59, countries=None, companies=None, product_types=None
2025-06-27 17:24:03,439 - app.db.database - INFO - Executing query: SELECT * FROM gas_prices WHERE 1=1 AND date_recorded >= ? AND date_recorded <= ? ORDER BY date_recorded DESC
2025-06-27 17:24:03,440 - app.db.database - INFO - Query parameters: ['2025-05-28T00:00:00', '2025-06-27T23:59:59']
2025-06-27 17:24:03,783 - app.db.database - INFO - Query returned 1416 results
2025-06-27 17:24:03,784 - app.db.database - INFO - get_gas_prices called with: start_date=2025-05-28 00:00:00, end_date=2025-06-27 23:59:59, countries=None, companies=None, product_types=None
2025-06-27 17:24:03,784 - app.db.database - INFO - Executing query: SELECT * FROM gas_prices WHERE 1=1 AND date_recorded >= ? AND date_recorded <= ? ORDER BY date_recorded DESC
2025-06-27 17:24:03,784 - app.db.database - INFO - Query parameters: ['2025-05-28T00:00:00', '2025-06-27T23:59:59']
2025-06-27 17:24:03,836 - app.db.database - INFO - Query returned 1416 results
2025-06-27 17:24:04,034 - app.db.database - INFO - get_gas_prices called with: start_date=2025-05-28 00:00:00, end_date=2025-06-27 23:59:59, countries=None, companies=None, product_types=None
2025-06-27 17:24:04,034 - app.db.database - INFO - Executing query: SELECT * FROM gas_prices WHERE 1=1 AND date_recorded >= ? AND date_recorded <= ? ORDER BY date_recorded DESC
2025-06-27 17:24:04,035 - app.db.database - INFO - Query parameters: ['2025-05-28T00:00:00', '2025-06-27T23:59:59']
2025-06-27 17:24:04,076 - app.db.database - INFO - Query returned 1416 results
2025-06-27 17:24:04,077 - app.db.database - INFO - get_gas_prices called with: start_date=2025-05-28 00:00:00, end_date=2025-06-27 23:59:59, countries=None, companies=None, product_types=None
2025-06-27 17:24:04,077 - app.db.database - INFO - Executing query: SELECT * FROM gas_prices WHERE 1=1 AND date_recorded >= ? AND date_recorded <= ? ORDER BY date_recorded DESC
2025-06-27 17:24:04,077 - app.db.database - INFO - Query parameters: ['2025-05-28T00:00:00', '2025-06-27T23:59:59']
2025-06-27 17:24:04,119 - app.db.database - INFO - Query returned 1416 results
2025-06-27 17:24:49,419 - app.db.database - INFO - get_gas_prices called with: start_date=2025-05-28 00:00:00, end_date=2025-06-27 23:59:59, countries=None, companies=None, product_types=None
2025-06-27 17:24:49,419 - app.db.database - INFO - Executing query: SELECT * FROM gas_prices WHERE 1=1 AND date_recorded >= ? AND date_recorded <= ? ORDER BY date_recorded DESC
2025-06-27 17:24:49,419 - app.db.database - INFO - Query parameters: ['2025-05-28T00:00:00', '2025-06-27T23:59:59']
2025-06-27 17:24:49,469 - app.db.database - INFO - Query returned 1416 results
2025-06-27 17:24:49,469 - app.db.database - INFO - get_gas_prices called with: start_date=2025-05-28 00:00:00, end_date=2025-06-27 23:59:59, countries=None, companies=None, product_types=None
2025-06-27 17:24:49,470 - app.db.database - INFO - Executing query: SELECT * FROM gas_prices WHERE 1=1 AND date_recorded >= ? AND date_recorded <= ? ORDER BY date_recorded DESC
2025-06-27 17:24:49,470 - app.db.database - INFO - Query parameters: ['2025-05-28T00:00:00', '2025-06-27T23:59:59']
2025-06-27 17:24:49,516 - app.db.database - INFO - Query returned 1416 results
2025-06-27 17:24:49,646 - app.db.database - INFO - get_gas_prices called with: start_date=2025-05-28 00:00:00, end_date=2025-06-27 23:59:59, countries=None, companies=None, product_types=None
2025-06-27 17:24:49,646 - app.db.database - INFO - Executing query: SELECT * FROM gas_prices WHERE 1=1 AND date_recorded >= ? AND date_recorded <= ? ORDER BY date_recorded DESC
2025-06-27 17:24:49,646 - app.db.database - INFO - Query parameters: ['2025-05-28T00:00:00', '2025-06-27T23:59:59']
2025-06-27 17:24:49,694 - app.db.database - INFO - Query returned 1416 results
2025-06-27 17:24:49,694 - app.db.database - INFO - get_gas_prices called with: start_date=2025-05-28 00:00:00, end_date=2025-06-27 23:59:59, countries=None, companies=None, product_types=None
2025-06-27 17:24:49,695 - app.db.database - INFO - Executing query: SELECT * FROM gas_prices WHERE 1=1 AND date_recorded >= ? AND date_recorded <= ? ORDER BY date_recorded DESC
2025-06-27 17:24:49,695 - app.db.database - INFO - Query parameters: ['2025-05-28T00:00:00', '2025-06-27T23:59:59']
2025-06-27 17:24:49,742 - app.db.database - INFO - Query returned 1416 results
2025-06-27 17:27:23,636 - app.services.scheduler_service - INFO - Scheduler stop requested
2025-06-27 17:27:23,636 - app.main - INFO - Application shutdown successfully
2025-06-27 17:27:27,699 - app.services.email_service - INFO - Logging initialized
2025-06-27 17:27:27,738 - app.db.database - INFO - Database initialized successfully
2025-06-27 17:27:27,738 - app.services.scheduler_service - INFO - Starting scheduler at 2025-06-27T17:27:27.738711
2025-06-27 17:27:27,738 - app.services.scheduler_service - INFO - Scheduler started in background thread
2025-06-27 17:27:27,739 - app.main - INFO - Application started successfully
2025-06-27 17:27:27,741 - app.services.scheduler_service - INFO - Job scheduled to run daily at 15:08, next run: 2025-06-28 15:08:00
2025-06-27 17:28:17,193 - app.services.scheduler_service - INFO - Scheduler stop requested
2025-06-27 17:28:17,193 - app.main - INFO - Application shutdown successfully
2025-06-27 17:28:20,633 - app.services.email_service - INFO - Logging initialized
2025-06-27 17:28:20,707 - app.db.database - INFO - Database initialized successfully
2025-06-27 17:28:20,709 - app.services.scheduler_service - INFO - Starting scheduler at 2025-06-27T17:28:20.708992
2025-06-27 17:28:20,709 - app.services.scheduler_service - INFO - Scheduler started in background thread
2025-06-27 17:28:20,709 - app.main - INFO - Application started successfully
2025-06-27 17:28:20,712 - app.services.scheduler_service - INFO - Job scheduled to run daily at 15:08, next run: 2025-06-28 15:08:00
2025-06-27 17:28:23,850 - app.services.scheduler_service - INFO - Scheduler stop requested
2025-06-27 17:28:23,850 - app.main - INFO - Application shutdown successfully
2025-06-27 17:28:27,354 - app.services.email_service - INFO - Logging initialized
2025-06-27 17:28:27,396 - app.db.database - INFO - Database initialized successfully
2025-06-27 17:28:27,397 - app.services.scheduler_service - INFO - Starting scheduler at 2025-06-27T17:28:27.397289
2025-06-27 17:28:27,397 - app.services.scheduler_service - INFO - Scheduler started in background thread
2025-06-27 17:28:27,397 - app.main - INFO - Application started successfully
2025-06-27 17:28:27,399 - app.services.scheduler_service - INFO - Job scheduled to run daily at 15:08, next run: 2025-06-28 15:08:00
2025-06-27 17:28:30,418 - app.services.scheduler_service - INFO - Scheduler stop requested
2025-06-27 17:28:30,418 - app.main - INFO - Application shutdown successfully
2025-06-27 17:28:33,834 - app.services.email_service - INFO - Logging initialized
2025-06-27 17:28:33,873 - app.db.database - INFO - Database initialized successfully
2025-06-27 17:28:33,874 - app.services.scheduler_service - INFO - Starting scheduler at 2025-06-27T17:28:33.874658
2025-06-27 17:28:33,874 - app.services.scheduler_service - INFO - Scheduler started in background thread
2025-06-27 17:28:33,875 - app.main - INFO - Application started successfully
2025-06-27 17:28:33,876 - app.services.scheduler_service - INFO - Job scheduled to run daily at 15:08, next run: 2025-06-28 15:08:00
2025-06-27 17:28:43,886 - app.db.database - INFO - get_gas_prices called with: start_date=2025-05-28 00:00:00, end_date=2025-06-27 23:59:59, countries=None, companies=None, product_types=None
2025-06-27 17:28:43,887 - app.db.database - INFO - Executing query: SELECT * FROM gas_prices WHERE 1=1 AND date_recorded >= ? AND date_recorded <= ? ORDER BY date_recorded DESC
2025-06-27 17:28:43,887 - app.db.database - INFO - Query parameters: ['2025-05-28T00:00:00', '2025-06-27T23:59:59']
2025-06-27 17:28:44,036 - app.db.database - INFO - Query returned 1416 results
2025-06-27 17:28:44,036 - app.db.database - INFO - get_gas_prices called with: start_date=2025-05-28 00:00:00, end_date=2025-06-27 23:59:59, countries=None, companies=None, product_types=None
2025-06-27 17:28:44,037 - app.db.database - INFO - Executing query: SELECT * FROM gas_prices WHERE 1=1 AND date_recorded >= ? AND date_recorded <= ? ORDER BY date_recorded DESC
2025-06-27 17:28:44,037 - app.db.database - INFO - Query parameters: ['2025-05-28T00:00:00', '2025-06-27T23:59:59']
2025-06-27 17:28:44,091 - app.db.database - INFO - Query returned 1416 results
2025-06-27 17:28:44,318 - app.db.database - INFO - get_gas_prices called with: start_date=2025-05-28 00:00:00, end_date=2025-06-27 23:59:59, countries=None, companies=None, product_types=None
2025-06-27 17:28:44,319 - app.db.database - INFO - Executing query: SELECT * FROM gas_prices WHERE 1=1 AND date_recorded >= ? AND date_recorded <= ? ORDER BY date_recorded DESC
2025-06-27 17:28:44,319 - app.db.database - INFO - Query parameters: ['2025-05-28T00:00:00', '2025-06-27T23:59:59']
2025-06-27 17:28:44,375 - app.db.database - INFO - Query returned 1416 results
2025-06-27 17:28:44,376 - app.db.database - INFO - get_gas_prices called with: start_date=2025-05-28 00:00:00, end_date=2025-06-27 23:59:59, countries=None, companies=None, product_types=None
2025-06-27 17:28:44,376 - app.db.database - INFO - Executing query: SELECT * FROM gas_prices WHERE 1=1 AND date_recorded >= ? AND date_recorded <= ? ORDER BY date_recorded DESC
2025-06-27 17:28:44,377 - app.db.database - INFO - Query parameters: ['2025-05-28T00:00:00', '2025-06-27T23:59:59']
2025-06-27 17:28:44,432 - app.db.database - INFO - Query returned 1416 results
2025-06-27 17:28:59,066 - app.services.scheduler_service - INFO - Scheduler stop requested
2025-06-27 17:28:59,066 - app.main - INFO - Application shutdown successfully
2025-06-27 17:29:03,379 - app.services.email_service - INFO - Logging initialized
2025-06-27 17:29:03,419 - app.db.database - INFO - Database initialized successfully
2025-06-27 17:29:03,420 - app.services.scheduler_service - INFO - Starting scheduler at 2025-06-27T17:29:03.420039
2025-06-27 17:29:03,420 - app.services.scheduler_service - INFO - Scheduler started in background thread
2025-06-27 17:29:03,420 - app.main - INFO - Application started successfully
2025-06-27 17:29:03,422 - app.services.scheduler_service - INFO - Job scheduled to run daily at 15:08, next run: 2025-06-28 15:08:00
2025-06-27 17:29:10,278 - app.services.scheduler_service - INFO - Scheduler stop requested
2025-06-27 17:29:10,278 - app.main - INFO - Application shutdown successfully
2025-06-27 17:29:13,775 - app.services.email_service - INFO - Logging initialized
2025-06-27 17:29:13,815 - app.db.database - INFO - Database initialized successfully
2025-06-27 17:29:13,816 - app.services.scheduler_service - INFO - Starting scheduler at 2025-06-27T17:29:13.816136
2025-06-27 17:29:13,816 - app.services.scheduler_service - INFO - Scheduler started in background thread
2025-06-27 17:29:13,816 - app.main - INFO - Application started successfully
2025-06-27 17:29:13,818 - app.services.scheduler_service - INFO - Job scheduled to run daily at 15:08, next run: 2025-06-28 15:08:00
2025-06-27 17:29:20,415 - app.services.scheduler_service - INFO - Scheduler stop requested
2025-06-27 17:29:20,415 - app.main - INFO - Application shutdown successfully
