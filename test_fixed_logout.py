#!/usr/bin/env python3
"""
Test the fixed logout functionality.
"""

import requests
from urllib.parse import urljoin
import time

BASE_URL = "http://localhost:8000"
ADMIN_USERNAME = "admin"
ADMIN_PASSWORD = "adminrubis2025"

def test_fixed_logout():
    """Test the fixed logout functionality"""
    
    print("=== Testing Fixed Logout Functionality ===")
    
    session = requests.Session()
    
    # Step 1: Login
    print("1. Logging in...")
    login_response = session.post(
        urljoin(BASE_URL, "/auth/login"),
        data={"username": ADMIN_USERNAME, "password": ADMIN_PASSWORD},
        allow_redirects=False
    )
    
    if login_response.status_code != 302:
        print(f"   ❌ Login failed: {login_response.status_code}")
        return False
    
    print("   ✅ Login successful")
    
    # Step 2: Access protected page
    print("2. Accessing protected page...")
    manual_request = session.get(urljoin(BASE_URL, "/manual-request"))
    
    if manual_request.status_code == 200 and ADMIN_USERNAME in manual_request.text:
        print("   ✅ Protected page accessible")
    else:
        print(f"   ❌ Protected page access failed: {manual_request.status_code}")
        return False
    
    # Step 3: Test logout with new implementation
    print("3. Testing logout with fixed implementation...")
    logout_response = session.post(
        urljoin(BASE_URL, "/auth/logout"),
        allow_redirects=False
    )
    
    print(f"   Logout status: {logout_response.status_code}")
    print(f"   Logout headers: {dict(logout_response.headers)}")
    
    # Check for both regular redirect and HTMX redirect
    location = logout_response.headers.get('Location', '')
    hx_redirect = logout_response.headers.get('HX-Redirect', '')
    
    if logout_response.status_code == 302:
        print(f"   ✅ Logout redirect to: {location}")
        if hx_redirect:
            print(f"   ✅ HTMX redirect header: {hx_redirect}")
        else:
            print("   ⚠️  No HTMX redirect header")
    else:
        print(f"   ❌ Logout failed: {logout_response.status_code}")
    
    # Step 4: Verify authentication is cleared
    print("4. Verifying authentication is cleared...")
    test_protected = session.get(urljoin(BASE_URL, "/manual-request"), allow_redirects=False)
    
    if test_protected.status_code == 302:
        print("   ✅ Authentication cleared - redirected to login")
    else:
        print(f"   ❌ Authentication not cleared - status: {test_protected.status_code}")
    
    # Step 5: Test logout from different pages
    print("5. Testing logout from different pages...")
    
    test_pages = [
        ("/manual-request", "Manual Request"),
        ("/admin", "Admin Panel"),
        ("/admin/sites", "Site Configuration")
    ]
    
    for page_url, page_name in test_pages:
        print(f"   Testing logout from {page_name}...")
        
        # Login again
        session.post(
            urljoin(BASE_URL, "/auth/login"),
            data={"username": ADMIN_USERNAME, "password": ADMIN_PASSWORD}
        )
        
        # Access the page
        page_response = session.get(urljoin(BASE_URL, page_url))
        if page_response.status_code != 200:
            print(f"     ❌ Cannot access {page_name}: {page_response.status_code}")
            continue
        
        # Check for hx-boost="false" in logout form
        if 'hx-boost="false"' in page_response.text:
            print(f"     ✅ HTMX boost disabled for logout form on {page_name}")
        else:
            print(f"     ❌ HTMX boost not disabled on {page_name}")
        
        # Test logout
        logout_resp = session.post(urljoin(BASE_URL, "/auth/logout"), allow_redirects=False)
        if logout_resp.status_code == 302:
            print(f"     ✅ Logout successful from {page_name}")
            
            # Check headers
            if logout_resp.headers.get('HX-Redirect'):
                print(f"     ✅ HTMX redirect header present")
            else:
                print(f"     ❌ HTMX redirect header missing")
        else:
            print(f"     ❌ Logout failed from {page_name}: {logout_resp.status_code}")
    
    return True

def test_logout_with_htmx_headers():
    """Test logout with HTMX headers to simulate browser behavior"""
    
    print("\n=== Testing Logout with HTMX Headers ===")
    
    session = requests.Session()
    
    # Login
    session.post(
        urljoin(BASE_URL, "/auth/login"),
        data={"username": ADMIN_USERNAME, "password": ADMIN_PASSWORD}
    )
    
    # Test logout with HTMX headers
    print("1. Testing logout with HTMX headers...")
    logout_response = session.post(
        urljoin(BASE_URL, "/auth/logout"),
        headers={
            "HX-Request": "true",
            "HX-Current-URL": f"{BASE_URL}/manual-request"
        },
        allow_redirects=False
    )
    
    print(f"   Status: {logout_response.status_code}")
    print(f"   Headers: {dict(logout_response.headers)}")
    
    if logout_response.headers.get('HX-Redirect'):
        print("   ✅ HTMX redirect header present for HTMX request")
    else:
        print("   ❌ HTMX redirect header missing for HTMX request")

if __name__ == "__main__":
    try:
        test_fixed_logout()
        test_logout_with_htmx_headers()
    except requests.exceptions.ConnectionError:
        print(f"❌ Could not connect to {BASE_URL}")
        print("Make sure the server is running")
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
