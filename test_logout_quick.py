#!/usr/bin/env python3
"""
Quick test of logout functionality.
"""

import requests
from urllib.parse import urljoin

BASE_URL = "http://localhost:8000"
ADMIN_USERNAME = "admin"
ADMIN_PASSWORD = "adminrubis2025"

def test_logout():
    """Quick logout test"""
    
    print("=== Quick Logout Test ===")
    
    session = requests.Session()
    
    # Login
    print("1. Logging in...")
    login_response = session.post(
        urljoin(BASE_URL, "/auth/login"),
        data={"username": ADMIN_USERNAME, "password": ADMIN_PASSWORD}
    )
    print(f"   Login status: {login_response.status_code}")
    
    # Test logout
    print("2. Testing logout...")
    logout_response = session.post(
        urljoin(BASE_URL, "/auth/logout"),
        allow_redirects=False
    )
    
    print(f"   Logout status: {logout_response.status_code}")
    print(f"   Location: {logout_response.headers.get('Location', 'None')}")
    print(f"   HX-Redirect: {logout_response.headers.get('HX-Redirect', 'None')}")
    print(f"   Set-Cookie: {logout_response.headers.get('Set-Cookie', 'None')}")
    
    # Test access after logout
    print("3. Testing access after logout...")
    test_response = session.get(urljoin(BASE_URL, "/manual-request"), allow_redirects=False)
    print(f"   Status: {test_response.status_code}")
    print(f"   Location: {test_response.headers.get('Location', 'None')}")
    
    if test_response.status_code == 302:
        print("   ✅ Logout working - redirected to login")
    else:
        print("   ❌ Logout not working")

if __name__ == "__main__":
    try:
        test_logout()
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
