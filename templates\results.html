<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Scraper Results - <PERSON> Price Scraper</title>
    <link rel="icon" href="/static/media/favicon.ico" type="image/x-icon">
    <script src="https://unpkg.com/htmx.org@2.0.4" integrity="sha384-HGfztofotfshcF7+8n44JQL2oJmowVChPTg48S+jvZoztPfvwD79OC/LTtG6dMp+" crossorigin="anonymous"></script>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <!-- Authentication Header -->
        {% include "auth_header.html" %}

        <header class="mb-8">
            <h1 class="text-4xl font-bold text-center text-blue-600"><PERSON> Price Scraper</h1>
            <p class="text-center text-gray-600 mt-2">View scraper results</p>
        </header>

        <nav class="flex justify-center space-x-6 mb-8">
            <a href="/database" class="text-blue-600 font-medium hover:text-blue-800">Database View</a>
            <a href="/results" class="text-blue-600 font-medium hover:text-blue-800">View All Results</a>
        </nav>

        <div class="bg-white rounded-lg shadow-md p-6">
            <h2 class="text-2xl font-semibold mb-4">Available Results</h2>
            
            <div id="results-table">
                {% if files %}
                    <div class="overflow-x-auto">
                        <table class="min-w-full bg-white">
                            <thead>
                                <tr>
                                    <th class="py-3 px-4 text-left border-b-2 border-gray-200 bg-gray-100 text-gray-600">File Name</th>
                                    <th class="py-3 px-4 text-left border-b-2 border-gray-200 bg-gray-100 text-gray-600">Date</th>
                                    <th class="py-3 px-4 text-center border-b-2 border-gray-200 bg-gray-100 text-gray-600">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for file in files %}
                                <tr class="hover:bg-gray-50">
                                    <td class="py-3 px-4 border-b border-gray-200">{{ file.name }}</td>
                                    <td class="py-3 px-4 border-b border-gray-200">{{ file.date }}</td>
                                    <td class="py-3 px-4 border-b border-gray-200 text-center">
                                        <a href="/results/{{ file.name }}" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-1 px-3 rounded-md transition duration-300">
                                            View
                                        </a>
                                        <a href="/static/results/{{ file.name }}" download class="bg-green-600 hover:bg-green-700 text-white font-medium py-1 px-3 rounded-md transition duration-300 ml-2">
                                            Download
                                        </a>
                                        <button
                                            hx-delete="/results/{{ file.name }}"
                                            hx-confirm="Are you sure you want to delete this file?"
                                            hx-target="#results-table"
                                            hx-swap="innerHTML"
                                            class="bg-red-600 hover:bg-red-700 text-white font-medium py-1 px-3 rounded-md transition duration-300 ml-2 cursor-pointer">
                                                Delete
                                        </button>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-8">
                        <p class="text-gray-600">No results available yet. Run the scraper first.</p>
                        <a href="/" class="inline-block mt-4 bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition duration-300">
                            Go to Scraper
                        </a>
                    </div>
                {% endif %}
            </div>

            <div class="mt-4 flex items-center justify-between px-4 py-3 bg-white sm:px-6">
                <div class="flex justify-between flex-1 sm:hidden">
                    {% if pagination.current_page > 1 %}
                    <a href="/results?page={{ pagination.current_page - 1 }}&per_page={{ pagination.per_page }}"
                       class="relative inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                        Previous
                    </a>
                    {% endif %}
                    {% if pagination.current_page < pagination.total_pages %}
                    <a href="/results?page={{ pagination.current_page + 1 }}&per_page={{ pagination.per_page }}"
                       class="relative inline-flex items-center px-4 py-2 ml-3 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                        Next
                    </a>
                    {% endif %}
                </div>
                <div class="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
                    <div>
                        <p class="text-sm text-gray-700">
                            Showing
                            <span class="font-medium">{{ (pagination.current_page - 1) * pagination.per_page + 1 }}</span>
                            to
                            <span class="font-medium">{{ min(pagination.current_page * pagination.per_page, pagination.total_records) }}</span>
                            of
                            <span class="font-medium">{{ pagination.total_records }}</span>
                            results
                        </p>
                    </div>
                    <div>
                        <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                            {% if pagination.current_page > 1 %}
                            <a href="/results?page={{ pagination.current_page - 1 }}&per_page={{ pagination.per_page }}"
                               class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                <span class="sr-only">Previous</span>
                                <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                    <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                                </svg>
                            </a>
                            {% endif %}

                            {% for p in range(max(1, pagination.current_page - 2), min(pagination.total_pages + 1, pagination.current_page + 3)) %}
                            <a href="/results?page={{ p }}&per_page={{ pagination.per_page }}"
                               class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium {% if p == pagination.current_page %}text-indigo-600 bg-indigo-50{% else %}text-gray-700 hover:bg-gray-50{% endif %}">
                                {{ p }}
                            </a>
                            {% endfor %}

                            {% if pagination.current_page < pagination.total_pages %}
                            <a href="/results?page={{ pagination.current_page + 1 }}&per_page={{ pagination.per_page }}"
                               class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                <span class="sr-only">Next</span>
                                <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                                </svg>
                            </a>
                            {% endif %}
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Job status indicator in the corner - only loaded once when a job starts -->
    <div id="job-indicator-container" hx-get="/job-indicator" hx-trigger="load"></div>
</body>
</html>

