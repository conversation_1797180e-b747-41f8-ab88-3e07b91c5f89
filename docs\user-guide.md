# User Guide - Gas Bottle Price Scraper

This guide explains how to use the Gas Bottle Price Scraper web interface and features.

## Table of Contents

1. [Getting Started](#getting-started)
2. [Web Interface Overview](#web-interface-overview)
3. [Viewing Price Data](#viewing-price-data)
4. [Running Manual Scraping](#running-manual-scraping)
5. [Generating Reports](#generating-reports)
6. [Admin Panel](#admin-panel)
7. [Understanding the Data](#understanding-the-data)
8. [Troubleshooting](#troubleshooting)

## Getting Started

### Accessing the Application

1. Open your web browser
2. Navigate to the application URL (e.g., `http://localhost:8000`)
3. You'll be automatically redirected to the database view

### System Requirements

- Modern web browser (Chrome, Firefox, Safari, Edge)
- Internet connection for real-time data
- JavaScript enabled for interactive features

## Web Interface Overview

The application provides several main sections:

### Navigation Menu

- **Database**: View and filter price data
- **Manual Request**: Trigger scraping manually
- **Results**: View historical scraping results
- **Admin**: Configuration and scheduling (requires authentication)

### Key Features

- **Real-time Data**: Live price information from multiple sources
- **Interactive Filtering**: Filter by country, company, product type, and date
- **Export Capabilities**: Generate Excel reports with charts
- **Responsive Design**: Works on desktop, tablet, and mobile devices

## Viewing Price Data

### Database View

The main database view shows current gas bottle prices with the following information:

| Column | Description |
|--------|-------------|
| Country | Source country (Portugal, Spain, France, etc.) |
| Company | Gas company name (Rubis, Galp, Repsol, etc.) |
| Product | Gas type (Butane or Propane) |
| Weight | Bottle weight in kg |
| Price | Current price in euros |
| Price/kg | Price per kilogram |
| Date | When the price was recorded |

### Filtering Data

Use the filter controls at the top of the page:

1. **Date Range**: Select start and end dates
   - Default shows last 30 days
   - Use date pickers for custom ranges

2. **Country Filter**: Choose specific countries
   - Select from dropdown list
   - Multiple countries can be selected

3. **Company Filter**: Filter by gas company
   - Shows only companies with available data
   - Useful for price comparisons

4. **Product Type Filter**: Filter by gas type
   - Butane: Commonly used for cooking
   - Propane: Used for heating and outdoor equipment

### Sorting and Search

- Click column headers to sort data
- Use browser search (Ctrl+F) to find specific items
- Data automatically updates with new scraping results

## Running Manual Scraping (Admin Only)

### Starting a Scraping Job

1. Navigate to "Manual Request" page
2. Click "Start Scraping" button
3. Monitor progress in real-time
4. View results when completed

### Job Status Indicators

- **Running**: Scraping in progress (blue indicator)
- **Completed**: Successfully finished (green indicator)
- **Failed**: Error occurred (red indicator)

### What Gets Scraped

The scraper automatically visits configured websites and extracts:
- Current gas bottle prices
- Product specifications
- Company information
- Regional pricing variations

### Scraping Duration

Typical scraping times:
- Single country: 2-5 minutes
- All countries: 10-15 minutes
- Depends on website response times

## Generating Reports

### Excel Report Generation

1. Go to Database view
2. Apply desired filters (optional)
3. Click "Generate Report" button
4. Download automatically starts

### Report Contents

The Excel report includes:

**Summary Sheet**:
- Total items scraped
- Countries and companies covered
- Price statistics (min, max, average)
- Date range of data

**Data Sheet**:
- Complete filtered dataset
- All columns from database view
- Formatted for analysis

**Charts Sheet**:
- Price comparison charts
- Country-wise price distribution
- Company comparison graphs
- Trend analysis (if date range selected)

### Report Customization

Filter data before generating reports to customize content:
- **Country-specific reports**: Filter by single country
- **Company comparisons**: Select multiple companies
- **Product analysis**: Filter by Butane or Propane
- **Time-based analysis**: Use date range filters

## Admin Panel

### Accessing Admin Features

1. Navigate to `/admin` URL
2. Enter admin credentials when prompted
3. Access configuration and scheduling features

**Note**: Admin access requires authentication with configured username/password.

### Configuration Management

**Scheduler Settings**:
- Set automatic scraping frequency
- Configure scraping times
- Enable/disable automatic runs

**Site Configuration**:
- Add new websites to scrape
- Modify existing site settings
- Test configuration changes

### Monitoring and Logs

**System Health**:
- View application status
- Check database connectivity
- Monitor system resources

**Scraping History**:
- View past scraping jobs
- Check success/failure rates
- Analyze performance metrics

## Understanding the Data

### Price Information

**Base Price**: Listed price from company website
**Price per kg**: Calculated rate for comparison
**Regional Variations**: Prices may vary by location within countries

### Data Accuracy

- Prices updated through automated scraping
- Data reflects website information at time of scraping
- Some sites may have regional pricing differences
- Prices include applicable taxes and fees

### Currency and Units

- All prices displayed in Euros (€)
- Weights shown in kilograms (kg)
- Dates in YYYY-MM-DD format
- Times in 24-hour format

### Data Freshness

- **Real-time**: Data from latest scraping run
- **Historical**: Previous scraping results available
- **Update Frequency**: Configurable (default: hourly)
- **Data Retention**: Configurable (default: 30 days)

## Troubleshooting

### Common Issues

**1. No Data Showing**
- Check if scraping has been run recently
- Verify date range filters aren't too restrictive
- Try refreshing the page

**2. Slow Loading**
- Large date ranges may take time to load
- Try filtering by country or company first
- Check internet connection

**3. Report Generation Fails**
- Ensure data exists for selected filters
- Try with smaller date range
- Check browser download settings

**4. Scraping Jobs Fail**
- Website may be temporarily unavailable
- Check admin panel for error details
- Try manual scraping again later

### Browser Compatibility

**Supported Browsers**:
- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

**Required Features**:
- JavaScript enabled
- Cookies enabled
- Modern CSS support

### Performance Tips

**For Better Performance**:
- Use specific date ranges instead of "all time"
- Filter by country/company when possible
- Close unused browser tabs
- Clear browser cache if issues persist

### Getting Help

**Self-Service Options**:
1. Check this user guide
2. Review error messages carefully
3. Try refreshing the page
4. Clear browser cache and cookies

**Contact Support**:
- Check application logs (admin users)
- Report issues with specific error messages
- Include browser and operating system information
- Provide steps to reproduce the issue

### Data Export Tips

**Excel Reports**:
- Use filters to limit data size
- Reports work best with < 10,000 records
- Include date ranges for trend analysis
- Save reports locally for offline analysis

**CSV Export** (if available):
- Better for large datasets
- Import into other analysis tools
- Preserves all data formatting
- Smaller file sizes than Excel

### Mobile Usage

**Mobile-Friendly Features**:
- Responsive design adapts to screen size
- Touch-friendly interface elements
- Simplified navigation on small screens
- Core functionality available on mobile

**Mobile Limitations**:
- Excel report generation may be slower
- Large datasets may impact performance
- Admin panel optimized for desktop use

For additional help or feature requests, contact your system administrator or check the project documentation.
