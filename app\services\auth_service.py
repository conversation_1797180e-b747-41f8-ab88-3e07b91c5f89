"""
Unified authentication service that extends HTTP Basic Auth with web-friendly features.

This module provides authentication functionality that works with both
HTTP Basic Auth (for API/admin routes) and web forms (for manual-request).
It maintains consistency by using the same credentials and validation logic.
"""

import logging
import base64
from typing import Optional, <PERSON><PERSON>
from secrets import compare_digest
from fastapi import Request, HTTPException, status, Depends
from fastapi.security import HTTPBasic, HTTPBasicCredentials
from fastapi.responses import RedirectResponse

from app.core.config import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()

# Global HTTP Basic security instance (same as main.py)
security = HTTPBasic()


class UnifiedAuthService:
    """
    Unified authentication service that extends HTTP Basic Auth with web-friendly features.

    This service provides:
    1. Standard HTTP Basic Auth for admin/API routes (maintains existing behavior)
    2. Web-friendly authentication for manual-request (stores auth in secure cookies)
    3. Consistent credential validation across all routes
    4. Seamless integration with existing verify_admin pattern
    """

    @staticmethod
    def validate_credentials(username: str, password: str) -> bool:
        """
        Validate user credentials against configured admin credentials.

        This is the same validation logic used by verify_admin to ensure consistency.

        Args:
            username: The username to validate
            password: The password to validate

        Returns:
            bool: True if credentials are valid, False otherwise
        """
        try:
            is_username_correct = compare_digest(
                username.encode("utf8"),
                settings.ADMIN_USERNAME.encode("utf8")
            )
            is_password_correct = compare_digest(
                password.encode("utf8"),
                settings.ADMIN_PASSWORD.encode("utf8")
            )

            return is_username_correct and is_password_correct
        except Exception as e:
            logger.error(f"Error validating credentials: {str(e)}")
            return False

    @staticmethod
    def create_auth_cookie_value(username: str, password: str) -> str:
        """
        Create a secure cookie value containing HTTP Basic Auth credentials.

        Args:
            username: The username
            password: The password

        Returns:
            str: Base64 encoded credentials for cookie storage
        """
        credentials = f"{username}:{password}"
        return base64.b64encode(credentials.encode()).decode()

    @staticmethod
    def extract_credentials_from_request(request: Request) -> Optional[Tuple[str, str]]:
        """
        Extract credentials from request (Authorization header or auth cookie).

        Args:
            request: The FastAPI request object

        Returns:
            Optional[Tuple[str, str]]: (username, password) if found, None otherwise
        """
        try:
            # First check Authorization header (standard HTTP Basic Auth)
            auth_header = request.headers.get("Authorization")
            if auth_header and auth_header.startswith("Basic "):
                encoded_credentials = auth_header[6:]  # Remove "Basic "
                decoded_credentials = base64.b64decode(encoded_credentials).decode()
                username, password = decoded_credentials.split(":", 1)
                return username, password

            # Then check for auth cookie (web-friendly storage)
            auth_cookie = request.cookies.get("auth_credentials")
            if auth_cookie:
                try:
                    decoded_credentials = base64.b64decode(auth_cookie).decode()
                    username, password = decoded_credentials.split(":", 1)
                    return username, password
                except Exception:
                    # Invalid cookie format, ignore
                    pass

            return None
        except Exception as e:
            logger.error(f"Error extracting credentials from request: {str(e)}")
            return None

    @staticmethod
    def is_authenticated(request: Request) -> bool:
        """
        Check if the current request is authenticated using unified approach.

        This checks both HTTP Basic Auth headers and auth cookies.

        Args:
            request: The FastAPI request object

        Returns:
            bool: True if authenticated, False otherwise
        """
        try:
            credentials = UnifiedAuthService.extract_credentials_from_request(request)
            if credentials:
                username, password = credentials
                return UnifiedAuthService.validate_credentials(username, password)
            return False
        except Exception as e:
            logger.error(f"Error checking authentication: {str(e)}")
            return False

    @staticmethod
    def get_current_username(request: Request) -> Optional[str]:
        """
        Get the current authenticated username.

        Args:
            request: The FastAPI request object

        Returns:
            Optional[str]: The username if authenticated, None otherwise
        """
        try:
            credentials = UnifiedAuthService.extract_credentials_from_request(request)
            if credentials:
                username, password = credentials
                if UnifiedAuthService.validate_credentials(username, password):
                    return username
            return None
        except Exception as e:
            logger.error(f"Error getting current username: {str(e)}")
            return None

    @staticmethod
    def get_redirect_after_login(request: Request) -> str:
        """
        Get the URL to redirect to after successful login.

        Args:
            request: The FastAPI request object

        Returns:
            str: The URL to redirect to (default: /manual-request)
        """
        try:
            redirect_url = request.session.pop("redirect_after_login", "/manual-request")
            return redirect_url
        except Exception as e:
            logger.error(f"Error getting redirect URL: {str(e)}")
            return "/manual-request"

    @staticmethod
    def store_redirect_url(request: Request, url: str) -> None:
        """
        Store a URL to redirect to after login.

        Args:
            request: The FastAPI request object
            url: The URL to redirect to after login
        """
        try:
            request.session["redirect_after_login"] = url
        except Exception as e:
            logger.error(f"Error storing redirect URL: {str(e)}")


# Web-friendly authentication dependency that extends HTTP Basic Auth
def verify_web_auth(request: Request) -> str:
    """
    FastAPI dependency for web-friendly authentication.

    This extends the existing HTTP Basic Auth pattern to work with both:
    1. Standard HTTP Basic Auth headers (for API/admin routes)
    2. Secure auth cookies (for web forms like manual-request)

    Args:
        request: The FastAPI request object

    Returns:
        str: The authenticated username

    Raises:
        HTTPException: Redirects to login for web requests, 401 for API requests
    """
    if not UnifiedAuthService.is_authenticated(request):
        # Store the original URL for redirect after login
        original_url = str(request.url)
        UnifiedAuthService.store_redirect_url(request, original_url)

        # Always redirect web requests to login page
        # This creates a proper redirect response that browsers will follow
        from fastapi.responses import RedirectResponse
        raise HTTPException(
            status_code=status.HTTP_302_FOUND,
            detail="Redirecting to login",
            headers={"Location": "/login"}
        )

    return UnifiedAuthService.get_current_username(request)


# Enhanced verify_admin that works with both HTTP Basic Auth and auth cookies
def verify_admin_unified(request: Request) -> str:
    """
    Enhanced admin authentication that supports both HTTP Basic Auth and auth cookies.

    This maintains backward compatibility with existing HTTP Basic Auth while
    also supporting the unified auth cookies for consistency.

    Args:
        request: The FastAPI request object

    Returns:
        str: The authenticated username

    Raises:
        HTTPException: 401 if not authenticated
    """
    try:
        # Try unified auth first (handles both HTTP Basic Auth and cookies)
        if UnifiedAuthService.is_authenticated(request):
            return UnifiedAuthService.get_current_username(request)

        # If not authenticated, raise error
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid credentials",
            headers={"WWW-Authenticate": "Basic"},
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Authentication error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid credentials",
            headers={"WWW-Authenticate": "Basic"},
        )


# Backward compatibility alias
AuthService = UnifiedAuthService
