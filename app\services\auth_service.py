"""
Authentication service for session-based authentication
"""

import logging
from typing import Optional
from secrets import compare_digest
from fastapi import Request, HTTPException, status
from fastapi.responses import RedirectResponse

from app.core.config import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()


class AuthService:
    """Service for handling session-based authentication"""
    
    SESSION_KEY = "authenticated"
    USERNAME_KEY = "username"
    
    @staticmethod
    def validate_credentials(username: str, password: str) -> bool:
        """
        Validate user credentials against configured admin credentials
        
        Args:
            username: The username to validate
            password: The password to validate
            
        Returns:
            bool: True if credentials are valid, False otherwise
        """
        try:
            is_username_correct = compare_digest(
                username.encode("utf8"),
                settings.ADMIN_USERNAME.encode("utf8")
            )
            is_password_correct = compare_digest(
                password.encode("utf8"),
                settings.ADMIN_PASSWORD.encode("utf8")
            )
            
            return is_username_correct and is_password_correct
        except Exception as e:
            logger.error(f"Error validating credentials: {str(e)}")
            return False
    
    @staticmethod
    def login_user(request: Request, username: str) -> None:
        """
        Log in a user by setting session data
        
        Args:
            request: The FastAPI request object
            username: The username to store in session
        """
        request.session[AuthService.SESSION_KEY] = True
        request.session[AuthService.USERNAME_KEY] = username
        logger.info(f"User {username} logged in successfully")
    
    @staticmethod
    def logout_user(request: Request) -> None:
        """
        Log out a user by clearing session data
        
        Args:
            request: The FastAPI request object
        """
        username = request.session.get(AuthService.USERNAME_KEY, "unknown")
        request.session.clear()
        logger.info(f"User {username} logged out successfully")
    
    @staticmethod
    def is_authenticated(request: Request) -> bool:
        """
        Check if the current user is authenticated
        
        Args:
            request: The FastAPI request object
            
        Returns:
            bool: True if user is authenticated, False otherwise
        """
        return request.session.get(AuthService.SESSION_KEY, False)
    
    @staticmethod
    def get_current_username(request: Request) -> Optional[str]:
        """
        Get the current authenticated username
        
        Args:
            request: The FastAPI request object
            
        Returns:
            Optional[str]: The username if authenticated, None otherwise
        """
        if AuthService.is_authenticated(request):
            return request.session.get(AuthService.USERNAME_KEY)
        return None
    
    @staticmethod
    def require_authentication(request: Request, redirect_url: str = "/login") -> None:
        """
        Require authentication, raise HTTPException if not authenticated
        
        Args:
            request: The FastAPI request object
            redirect_url: URL to redirect to if not authenticated
            
        Raises:
            HTTPException: 401 if not authenticated
        """
        if not AuthService.is_authenticated(request):
            # Store the original URL for redirect after login
            original_url = str(request.url)
            request.session["redirect_after_login"] = original_url
            
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Authentication required"
            )
    
    @staticmethod
    def get_redirect_after_login(request: Request) -> str:
        """
        Get the URL to redirect to after successful login
        
        Args:
            request: The FastAPI request object
            
        Returns:
            str: The URL to redirect to (default: /manual-request)
        """
        redirect_url = request.session.pop("redirect_after_login", "/manual-request")
        return redirect_url


# Dependency function for FastAPI routes
def get_auth_service() -> AuthService:
    """Dependency to get AuthService instance"""
    return AuthService()


# Dependency function to require authentication
def require_auth(request: Request, auth_service: AuthService = None) -> str:
    """
    FastAPI dependency that requires authentication
    
    Args:
        request: The FastAPI request object
        auth_service: The authentication service (injected)
        
    Returns:
        str: The authenticated username
        
    Raises:
        HTTPException: 401 if not authenticated
    """
    if auth_service is None:
        auth_service = AuthService()
    
    auth_service.require_authentication(request)
    return auth_service.get_current_username(request)
