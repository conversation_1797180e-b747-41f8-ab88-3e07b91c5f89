#!/usr/bin/env python3
"""
Debug script to test logout functionality and identify issues.
"""

import requests
from urllib.parse import urljoin
import time

BASE_URL = "http://localhost:8000"
ADMIN_USERNAME = "admin"
ADMIN_PASSWORD = "adminrubis2025"

def test_logout_functionality():
    """Test logout functionality step by step"""
    
    print("=== Testing Logout Functionality ===")
    
    session = requests.Session()
    
    # Step 1: Login
    print("1. Logging in...")
    login_response = session.post(
        urljoin(BASE_URL, "/auth/login"),
        data={"username": ADMIN_USERNAME, "password": ADMIN_PASSWORD},
        allow_redirects=False
    )
    
    if login_response.status_code != 302:
        print(f"   ❌ Login failed: {login_response.status_code}")
        return False
    
    auth_cookie = session.cookies.get("auth_credentials")
    print(f"   ✅ Login successful, auth cookie: {auth_cookie[:20] if auth_cookie else 'None'}...")
    
    # Step 2: Access protected page to verify authentication
    print("2. Accessing protected page...")
    manual_request = session.get(urljoin(BASE_URL, "/manual-request"))
    
    if manual_request.status_code == 200 and ADMIN_USERNAME in manual_request.text:
        print("   ✅ Protected page accessible with authentication")
    else:
        print(f"   ❌ Protected page access failed: {manual_request.status_code}")
        return False
    
    # Step 3: Test logout
    print("3. Testing logout...")
    logout_response = session.post(
        urljoin(BASE_URL, "/auth/logout"),
        allow_redirects=False
    )
    
    print(f"   Logout status: {logout_response.status_code}")
    print(f"   Logout headers: {dict(logout_response.headers)}")
    
    # Check if cookie was cleared
    auth_cookie_after = session.cookies.get("auth_credentials")
    print(f"   Auth cookie after logout: {auth_cookie_after}")
    
    if logout_response.status_code == 302:
        location = logout_response.headers.get('Location', '')
        print(f"   ✅ Logout redirect to: {location}")
    else:
        print(f"   ❌ Logout failed with status: {logout_response.status_code}")
        print(f"   Response content: {logout_response.text}")
    
    # Step 4: Test access to protected page after logout
    print("4. Testing protected page access after logout...")
    manual_request_after = session.get(urljoin(BASE_URL, "/manual-request"), allow_redirects=False)
    
    print(f"   Status: {manual_request_after.status_code}")
    if manual_request_after.status_code == 302:
        location = manual_request_after.headers.get('Location', '')
        print(f"   ✅ Properly redirected to: {location}")
    else:
        print(f"   ❌ Should redirect but got: {manual_request_after.status_code}")
    
    # Step 5: Test logout from different pages
    print("5. Testing logout from different pages...")
    
    # Login again
    session.post(
        urljoin(BASE_URL, "/auth/login"),
        data={"username": ADMIN_USERNAME, "password": ADMIN_PASSWORD}
    )
    
    test_pages = [
        ("/manual-request", "Manual Request"),
        ("/admin", "Admin Panel"),
        ("/admin/sites", "Site Configuration")
    ]
    
    for page_url, page_name in test_pages:
        print(f"   Testing logout from {page_name}...")
        
        # Access the page
        page_response = session.get(urljoin(BASE_URL, page_url))
        if page_response.status_code != 200:
            print(f"     ❌ Cannot access {page_name}: {page_response.status_code}")
            continue
        
        # Check if logout form exists
        if 'action="/auth/logout"' in page_response.text:
            print(f"     ✅ Logout form found on {page_name}")
        else:
            print(f"     ❌ Logout form missing on {page_name}")
        
        # Test logout
        logout_resp = session.post(urljoin(BASE_URL, "/auth/logout"), allow_redirects=False)
        if logout_resp.status_code == 302:
            print(f"     ✅ Logout successful from {page_name}")
        else:
            print(f"     ❌ Logout failed from {page_name}: {logout_resp.status_code}")
        
        # Re-login for next test
        session.post(
            urljoin(BASE_URL, "/auth/login"),
            data={"username": ADMIN_USERNAME, "password": ADMIN_PASSWORD}
        )
    
    return True

def test_cookie_clearing():
    """Test specific cookie clearing behavior"""
    print("\n=== Testing Cookie Clearing Behavior ===")
    
    session = requests.Session()
    
    # Login
    session.post(
        urljoin(BASE_URL, "/auth/login"),
        data={"username": ADMIN_USERNAME, "password": ADMIN_PASSWORD}
    )
    
    print(f"Cookies before logout: {dict(session.cookies)}")
    
    # Logout
    logout_response = session.post(urljoin(BASE_URL, "/auth/logout"), allow_redirects=False)
    
    print(f"Cookies after logout: {dict(session.cookies)}")
    print(f"Set-Cookie header: {logout_response.headers.get('Set-Cookie', 'None')}")
    
    # Check if the cookie is actually cleared by making another request
    test_response = session.get(urljoin(BASE_URL, "/manual-request"), allow_redirects=False)
    print(f"Test request after logout status: {test_response.status_code}")

if __name__ == "__main__":
    try:
        test_logout_functionality()
        test_cookie_clearing()
    except requests.exceptions.ConnectionError:
        print(f"❌ Could not connect to {BASE_URL}")
        print("Make sure the server is running")
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
