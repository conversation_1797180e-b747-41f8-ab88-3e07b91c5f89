<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin - <PERSON> Price Scraper</title>
    <link rel="icon" href="/static/media/favicon.ico" type="image/x-icon">
    <script src="https://unpkg.com/htmx.org@2.0.4"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.13/codemirror.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.13/codemirror.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.13/mode/javascript/javascript.min.js"></script>
</head>
<body class="bg-gray-100 min-h-screen">
    <!-- Include authentication header -->
    {% include "auth_header.html" %}

    <div class="container mx-auto px-4 py-8">
        <header class="mb-8">
            <h1 class="text-4xl font-bold text-center text-blue-600">Admin Panel</h1>
            <p class="text-center text-gray-600 mt-2">Edit Configuration</p>
        </header>

        <nav class="flex justify-center space-x-6 mb-8">
            <a href="/admin/sites" class="text-green-600 font-medium hover:text-green-800">Site Configuration</a>
            <a href="/manual-request" class="text-blue-600 font-medium hover:text-blue-800">Manual Request</a>
            <a href="/results" class="text-blue-600 font-medium hover:text-blue-800">View Results</a>
            <a href="/database" class="text-blue-600 font-medium hover:text-blue-800">View Database</a>
        </nav>

        <div class="bg-white rounded-lg shadow-md p-6">
            <form hx-post="/admin/save" hx-target="#status-message">
                <div class="mb-4">
                    <label for="config" class="block text-sm font-medium text-gray-700 mb-2">Configuration JSON</label>
                    <textarea 
                        id="config" 
                        name="config" 
                        rows="20" 
                        class="hidden"
                    >{{ config_json }}</textarea>
                </div>
                <div class="flex justify-between items-center">
                    <button 
                        type="submit" 
                        class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-6 rounded-md transition duration-300"
                    >
                        Save Changes
                    </button>
                    <button 
                        type="button"
                        hx-get="/admin/reload"
                        hx-target="#config"
                        class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-6 rounded-md transition duration-300"
                    >
                        Reload Original
                    </button>
                </div>
            </form>
            <div id="status-message" class="mt-4"></div>
        </div>
    </div>
    <script>
        document.addEventListener("DOMContentLoaded", function () {
            var configEditor = CodeMirror.fromTextArea(document.getElementById("config"), {
                mode: "application/json",
                lineNumbers: true,
                theme: "default",
                matchBrackets: true,
                autoCloseBrackets: true
            });
    
            // Update textarea on CodeMirror change
            configEditor.on("change", function() {
                document.getElementById("config").value = configEditor.getValue();
            });
    
            // Sync CodeMirror content to the textarea before form submission
            document.querySelector("form").addEventListener("submit", function (event) {
                // Ensure the textarea is updated right before submission
                document.getElementById("config").value = configEditor.getValue();
            });
        });
    </script>
</body>
</html>