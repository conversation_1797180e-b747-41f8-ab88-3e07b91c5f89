<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manual Request - Gas Bottle Price Scraper</title>
    <link rel="icon" href="/static/media/favicon.ico" type="image/x-icon">
    <script src="https://unpkg.com/htmx.org@2.0.4" integrity="sha384-HGfztofotfshcF7+8n44JQL2oJmowVChPTg48S+jvZoztPfvwD79OC/LTtG6dMp+" crossorigin="anonymous"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .htmx-indicator {
            display: none;
        }
        .htmx-request .htmx-indicator {
            display: inline;
        }
        .htmx-request.htmx-indicator {
            display: inline;
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen">
    <!-- Include authentication header -->
    {% include "auth_header.html" %}

    <div class="container mx-auto px-4 py-8">
        <header class="mb-8">
            <h1 class="text-4xl font-bold text-center text-blue-600">Manual Request</h1>
            <p class="text-center text-gray-600 mt-2">Get the latest prices for gas bottles across different countries and companies</p>
        </header>

        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-2xl font-semibold mb-4">Run Scraper</h2>
            <p class="mb-4 text-gray-700">Click the button below to start scraping the latest gas bottle prices. This process may take a few minutes to complete.</p>

            <!-- Advanced Options Section -->
            <div class="mb-6 border-t pt-4">
                <button
                    type="button"
                    onclick="toggleAdvancedOptions()"
                    class="flex items-center text-gray-600 hover:text-gray-800 font-medium mb-4"
                    id="advanced-toggle"
                >
                    <svg class="w-4 h-4 mr-2 transform transition-transform" id="advanced-arrow" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                    </svg>
                    Advanced Options
                </button>

                <div id="advanced-options" class="hidden bg-gray-50 rounded-lg p-4 space-y-4">
                    <!-- Test Run Mode -->
                    <div class="flex items-start space-x-3">
                        <div class="flex items-center h-5">
                            <input
                                id="test-mode"
                                name="test-mode"
                                type="checkbox"
                                class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
                            >
                        </div>
                        <div class="text-sm">
                            <label for="test-mode" class="font-medium text-gray-900">Test Run Mode</label>
                            <p class="text-gray-500">When enabled, scraped data will be saved to a downloadable CSV file instead of the database. Perfect for dry-runs and debugging without affecting production data.</p>
                        </div>
                    </div>

                    <!-- Target Selector -->
                    <div>
                        <label class="block text-sm font-medium text-gray-900 mb-2">Select Targets to Scrape</label>
                        <p class="text-xs text-gray-500 mb-3">Choose which countries, companies, and elements to scrape. All data fields will be extracted for selected targets.</p>

                        <!-- Country Selection -->
                        <div class="mb-4">
                            <h4 class="text-sm font-semibold text-gray-700 mb-2">Countries</h4>
                            <div class="grid grid-cols-2 md:grid-cols-4 gap-2 p-3 border border-gray-200 rounded-md bg-white">
                                <label class="flex items-center space-x-2">
                                    <input type="checkbox" name="countries" value="Switzerland" checked class="w-3 h-3 text-blue-600" onchange="updateCompanies()">
                                    <span class="text-sm">Switzerland</span>
                                </label>
                                <label class="flex items-center space-x-2">
                                    <input type="checkbox" name="countries" value="France" checked class="w-3 h-3 text-blue-600" onchange="updateCompanies()">
                                    <span class="text-sm">France</span>
                                </label>
                                <label class="flex items-center space-x-2">
                                    <input type="checkbox" name="countries" value="Germany" checked class="w-3 h-3 text-blue-600" onchange="updateCompanies()">
                                    <span class="text-sm">Germany</span>
                                </label>
                                <label class="flex items-center space-x-2">
                                    <input type="checkbox" name="countries" value="Italy" checked class="w-3 h-3 text-blue-600" onchange="updateCompanies()">
                                    <span class="text-sm">Italy</span>
                                </label>
                                <label class="flex items-center space-x-2">
                                    <input type="checkbox" name="countries" value="Spain" checked class="w-3 h-3 text-blue-600" onchange="updateCompanies()">
                                    <span class="text-sm">Spain</span>
                                </label>
                                <label class="flex items-center space-x-2">
                                    <input type="checkbox" name="countries" value="Portugal" checked class="w-3 h-3 text-blue-600" onchange="updateCompanies()">
                                    <span class="text-sm">Portugal</span>
                                </label>
                                <label class="flex items-center space-x-2">
                                    <input type="checkbox" name="countries" value="England" checked class="w-3 h-3 text-blue-600" onchange="updateCompanies()">
                                    <span class="text-sm">England</span>
                                </label>
                            </div>
                            <div class="flex space-x-2 mt-2">
                                <button type="button" onclick="selectAllCountries()" class="text-xs bg-blue-100 hover:bg-blue-200 text-blue-800 px-2 py-1 rounded">
                                    Select All
                                </button>
                                <button type="button" onclick="selectNoneCountries()" class="text-xs bg-gray-100 hover:bg-gray-200 text-gray-800 px-2 py-1 rounded">
                                    Select None
                                </button>
                            </div>
                        </div>

                        <!-- Company Selection -->
                        <div class="mb-4">
                            <h4 class="text-sm font-semibold text-gray-700 mb-2">Companies</h4>
                            <div id="companies-container" class="min-h-[60px] p-3 border border-gray-200 rounded-md bg-white">
                                <div class="text-sm text-gray-500 italic">Loading companies...</div>
                            </div>
                            <div class="flex space-x-2 mt-2">
                                <button type="button" onclick="selectAllCompanies()" class="text-xs bg-blue-100 hover:bg-blue-200 text-blue-800 px-2 py-1 rounded">
                                    Select All
                                </button>
                                <button type="button" onclick="selectNoneCompanies()" class="text-xs bg-gray-100 hover:bg-gray-200 text-gray-800 px-2 py-1 rounded">
                                    Select None
                                </button>
                            </div>
                        </div>

                        <!-- Element Selection -->
                        <div class="mb-4">
                            <h4 class="text-sm font-semibold text-gray-700 mb-2">Elements</h4>
                            <div id="elements-container" class="min-h-[80px] max-h-48 overflow-y-auto p-3 border border-gray-200 rounded-md bg-white">
                                <div class="text-sm text-gray-500 italic">Loading elements...</div>
                            </div>
                            <div class="flex space-x-2 mt-2">
                                <button type="button" onclick="selectAllElements()" class="text-xs bg-blue-100 hover:bg-blue-200 text-blue-800 px-2 py-1 rounded">
                                    Select All
                                </button>
                                <button type="button" onclick="selectNoneElements()" class="text-xs bg-gray-100 hover:bg-gray-200 text-gray-800 px-2 py-1 rounded">
                                    Select None
                                </button>
                            </div>
                        </div>

                        <!-- Selection Summary -->
                        <div id="selection-summary" class="p-3 bg-blue-50 border border-blue-200 rounded-md">
                            <div class="text-sm text-blue-800">
                                <strong>Selection Summary:</strong> <span id="summary-text">Loading...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="flex justify-center space-x-4">
                <button
                    hx-post="/run-scraper"
                    hx-target="#job-status"
                    hx-swap="innerHTML"
                    hx-include="[name='test-mode'], [name='countries'], [name='companies'], [name='elements']"
                    class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-6 rounded-md transition duration-300"
                    hx-indicator=".button-spinner"
                    id="start-button"
                >
                    <span id="button-text">Start Scraping</span>
                    <span class="htmx-indicator button-spinner ml-2">
                        <svg class="animate-spin h-5 w-5 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 714 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Loading...
                    </span>
                </button>
                <button
                    hx-post="/cancel-all-scrapers"
                    hx-target="#job-status"
                    hx-swap="beforeend"
                    class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-6 rounded-md transition duration-300 hidden"
                    hx-indicator=".button-spinner"
                    id="cancel-button"
                >
                    Cancel Scraping
                    <span class="htmx-indicator button-spinner ml-2">
                        <svg class="animate-spin h-5 w-5 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 714 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Loading...
                    </span>
                </button>
            </div>
        </div>

        <div id="job-status" class="mt-8"></div>
    </div>

    <!-- Include toast notifications -->
    {% include "toast_notifications.html" %}

    <!-- Job status indicator in the corner - only loaded once when a job starts -->
    <div id="job-indicator-container" hx-get="/job-indicator" hx-trigger="load"></div>

    <script>
        // Advanced Options Functions
        function toggleAdvancedOptions() {
            const options = document.getElementById('advanced-options');
            const arrow = document.getElementById('advanced-arrow');
            const isHidden = options.classList.contains('hidden');

            if (isHidden) {
                options.classList.remove('hidden');
                arrow.style.transform = 'rotate(90deg)';
                console.log('Advanced options expanded - loading companies');
                // Load companies when advanced options are first opened
                updateCompanies();
            } else {
                options.classList.add('hidden');
                arrow.style.transform = 'rotate(0deg)';
            }
        }

        // Country selection functions
        function selectAllCountries() {
            const checkboxes = document.querySelectorAll('input[name="countries"]');
            checkboxes.forEach(cb => cb.checked = true);
            updateCompanies();
        }

        function selectNoneCountries() {
            const checkboxes = document.querySelectorAll('input[name="countries"]');
            checkboxes.forEach(cb => cb.checked = false);
            updateCompanies();
        }

        // Company selection functions
        function selectAllCompanies() {
            const checkboxes = document.querySelectorAll('input[name="companies"]');
            checkboxes.forEach(cb => cb.checked = true);
            updateElements();
        }

        function selectNoneCompanies() {
            const checkboxes = document.querySelectorAll('input[name="companies"]');
            checkboxes.forEach(cb => cb.checked = false);
            updateElements();
        }

        // Element selection functions
        function selectAllElements() {
            const checkboxes = document.querySelectorAll('input[name="elements"]');
            checkboxes.forEach(cb => cb.checked = true);
            updateSelectionSummary();
        }

        function selectNoneElements() {
            const checkboxes = document.querySelectorAll('input[name="elements"]');
            checkboxes.forEach(cb => cb.checked = false);
            updateSelectionSummary();
        }

        // Update button text based on test mode
        function updateButtonText() {
            const testMode = document.getElementById('test-mode').checked;
            const buttonText = document.getElementById('button-text');

            if (testMode) {
                buttonText.textContent = 'Start Test Run (CSV Only)';
            } else {
                buttonText.textContent = 'Start Scraping';
            }
        }

        // Dynamic loading functions
        async function updateCompanies() {
            console.log('updateCompanies() called');
            const selectedCountries = Array.from(document.querySelectorAll('input[name="countries"]:checked')).map(cb => cb.value);
            console.log('Selected countries:', selectedCountries);
            const container = document.getElementById('companies-container');
            console.log('Companies container:', container);

            if (selectedCountries.length === 0) {
                console.log('No countries selected');
                container.innerHTML = '<div class="text-sm text-gray-500 italic">Select countries to see available companies</div>';
                updateElements();
                return;
            }

            container.innerHTML = '<div class="text-sm text-gray-500 italic">Loading companies...</div>';
            console.log('Making API request to /api/get-companies');

            try {
                const response = await fetch('/api/get-companies', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ countries: selectedCountries })
                });

                console.log('API response status:', response.status);
                const data = await response.json();
                console.log('API response data:', data);

                if (data.companies && data.companies.length > 0) {
                    console.log('Found companies:', data.companies.length);
                    const companiesHtml = data.companies.map(company => `
                        <label class="flex items-center space-x-2 mb-1">
                            <input type="checkbox" name="companies" value="${company}" checked class="w-3 h-3 text-blue-600" onchange="updateElements()">
                            <span class="text-sm">${company}</span>
                        </label>
                    `).join('');

                    container.innerHTML = `<div class="grid grid-cols-1 md:grid-cols-2 gap-1">${companiesHtml}</div>`;
                    console.log('Companies HTML updated');
                } else {
                    console.log('No companies found in response');
                    container.innerHTML = '<div class="text-sm text-gray-500 italic">No companies found for selected countries</div>';
                }
            } catch (error) {
                console.error('Error loading companies:', error);
                container.innerHTML = '<div class="text-sm text-red-500">Error loading companies</div>';
            }

            updateElements();
        }

        async function updateElements() {
            console.log('updateElements() called');
            const selectedCountries = Array.from(document.querySelectorAll('input[name="countries"]:checked')).map(cb => cb.value);
            const selectedCompanies = Array.from(document.querySelectorAll('input[name="companies"]:checked')).map(cb => cb.value);
            console.log('Selected countries for elements:', selectedCountries);
            console.log('Selected companies for elements:', selectedCompanies);
            const container = document.getElementById('elements-container');

            if (selectedCountries.length === 0 || selectedCompanies.length === 0) {
                console.log('Not enough selections for elements');
                container.innerHTML = '<div class="text-sm text-gray-500 italic">Select countries and companies to see available elements</div>';
                updateSelectionSummary();
                return;
            }

            container.innerHTML = '<div class="text-sm text-gray-500 italic">Loading elements...</div>';
            console.log('Making API request to /api/get-elements');

            try {
                const response = await fetch('/api/get-elements', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ countries: selectedCountries, companies: selectedCompanies })
                });

                console.log('Elements API response status:', response.status);
                const data = await response.json();
                console.log('Elements API response data:', data);

                if (data.elements && data.elements.length > 0) {
                    console.log('Found elements:', data.elements.length);
                    const elementsHtml = data.elements.map(element => `
                        <label class="flex items-center space-x-2 mb-1">
                            <input type="checkbox" name="elements" value="${element.key}" checked class="w-3 h-3 text-blue-600" onchange="updateSelectionSummary()">
                            <span class="text-sm">
                                <strong>${element.name}</strong>
                                <span class="text-gray-500">(${element.country} - ${element.company})</span>
                                ${element.uid ? `<span class="text-xs text-blue-600">[${element.uid}]</span>` : ''}
                            </span>
                        </label>
                    `).join('');

                    container.innerHTML = elementsHtml;
                    console.log('Elements HTML updated');
                } else {
                    console.log('No elements found in response');
                    container.innerHTML = '<div class="text-sm text-gray-500 italic">No elements found for selected countries and companies</div>';
                }
            } catch (error) {
                console.error('Error loading elements:', error);
                container.innerHTML = '<div class="text-sm text-red-500">Error loading elements</div>';
            }

            updateSelectionSummary();
        }

        function updateSelectionSummary() {
            const selectedCountries = document.querySelectorAll('input[name="countries"]:checked').length;
            const selectedCompanies = document.querySelectorAll('input[name="companies"]:checked').length;
            const selectedElements = document.querySelectorAll('input[name="elements"]:checked').length;

            const summaryText = `${selectedCountries} countries, ${selectedCompanies} companies, ${selectedElements} elements selected`;
            console.log('Selection summary:', summaryText);
            const summaryElement = document.getElementById('summary-text');
            if (summaryElement) {
                summaryElement.textContent = summaryText;
            } else {
                console.error('Summary text element not found');
            }
        }

        // Add event listener for test mode checkbox
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM Content Loaded - initializing target selection');
            const testModeCheckbox = document.getElementById('test-mode');
            if (testModeCheckbox) {
                testModeCheckbox.addEventListener('change', updateButtonText);
                console.log('Test mode checkbox listener added');
            } else {
                console.error('Test mode checkbox not found');
            }

            // Don't initialize companies immediately since Advanced Options are hidden
            // They will be loaded when the user expands the Advanced Options section
            console.log('Target selection initialized - companies will load when Advanced Options are expanded');
        });

        htmx.on("htmx:afterRequest", function(evt) {
            if (evt.detail.requestConfig.path === "/run-scraper") {
                // Show cancel button when scraper starts
                document.getElementById('cancel-button').classList.remove('hidden');
                // Optionally disable start button while scraping
                document.getElementById('start-button').setAttribute('hidden', 'hidden');
            }

            if (evt.detail.requestConfig.path === "/cancel-all-scrapers") {
                // Hide cancel button when scraping is cancelled
                document.getElementById('cancel-button').classList.add('hidden');
                // Re-enable start button
                document.getElementById('start-button').removeAttribute('hidden');
            }
        });

        // Listen for completion event
        htmx.on("htmx:afterSettle", function(evt) {
            if (evt.detail.requestConfig.path === "/run-scraper") {
                // Check if the response contains completion message
                if (evt.detail.innerHTML.includes("completed successfully")) {
                    document.getElementById('cancel-button').classList.add('hidden');
                    document.getElementById('start-button').removeAttribute('hidden');
                }
            }
        });

        // Toast notifications for scraper operations
        htmx.on("htmx:beforeRequest", function(evt) {
            if (evt.detail.requestConfig.path === "/run-scraper") {
                window.toastManager.info('Starting Scraper', 'Initializing gas bottle price scraping...', { duration: 0 });
            }
        });

        htmx.on("htmx:afterRequest", function(evt) {
            if (evt.detail.requestConfig.path === "/run-scraper") {
                // Clear any existing info toasts
                if (window.toastManager) {
                    window.toastManager.toasts.forEach((toast, id) => {
                        if (toast.classList.contains('info')) {
                            window.toastManager.hide(id);
                        }
                    });
                }

                if (evt.detail.xhr.status === 200) {
                    window.toastManager.success('Scraper Started', 'Gas bottle price scraping has been initiated successfully.');
                } else {
                    window.toastManager.error('Scraper Failed', 'There was an error starting the scraper. Please try again.');
                }
            }
        });

        // Listen for job completion via polling or server-sent events
        function checkJobStatus(jobId) {
            if (!jobId) return;

            fetch(`/job-status/${jobId}`)
                .then(response => response.text())
                .then(html => {
                    if (html.includes('completed successfully')) {
                        window.toastManager.success('Scraping Complete', 'Gas bottle price scraping has finished successfully!');
                    } else if (html.includes('failed')) {
                        window.toastManager.error('Scraping Failed', 'The scraping process encountered an error.');
                    } else if (html.includes('running')) {
                        // Continue polling
                        setTimeout(() => checkJobStatus(jobId), 5000);
                    }
                })
                .catch(error => {
                    console.error('Error checking job status:', error);
                });
        }

        // Extract job ID from response and start polling
        htmx.on("htmx:afterSettle", function(evt) {
            if (evt.detail.requestConfig.path === "/run-scraper") {
                const jobIdMatch = evt.detail.innerHTML.match(/job-status\/([^"]+)/);
                if (jobIdMatch) {
                    const jobId = jobIdMatch[1];
                    setTimeout(() => checkJobStatus(jobId), 2000); // Start checking after 2 seconds
                }
            }
        });
    </script>
</body>
</html>

